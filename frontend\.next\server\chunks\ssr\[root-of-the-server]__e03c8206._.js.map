{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Tüm hatırlatmaları getir\n  getAllReminders: async (): Promise<any[]> => {\n    const response = await api.get('/reminders/all');\n    return response.data;\n  },\n\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Hatırlatma sil\n  deleteReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.delete(`/reminders/${reminderId}`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/health/vaccines/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  ArrowLeft,\n  Shield,\n  Calendar,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Plus,\n  Search,\n  Filter\n} from 'lucide-react';\nimport { farmApi, animalApi, healthApi } from '@/services/api';\nimport { Farm, Animal } from '@/types';\n\nexport default function VaccineManagementPage() {\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [vaccineSchedules, setVaccineSchedules] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadFarmVaccineData();\n    }\n  }, [selectedFarmId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (!selectedFarmId && farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlik verileri yüklenirken hata oluştu');\n      console.error('Error loading farms:', err);\n    }\n  };\n\n  const loadFarmVaccineData = async () => {\n    try {\n      setLoading(true);\n\n      const animalsData = await animalApi.getAnimalsByFarm(selectedFarmId);\n      const safeAnimalsData = Array.isArray(animalsData) ? animalsData : [];\n      setAnimals(safeAnimalsData);\n\n      // Her hayvan için aşı takvimini yükle\n      const schedulePromises = safeAnimalsData.map(async (animal) => {\n        if (!animal || !animal.id) {\n          return {\n            animalId: 'unknown',\n            animalTag: 'Bilinmeyen Hayvan',\n            animalBreed: 'Bilinmeyen',\n            schedules: []\n          };\n        }\n\n        try {\n          const schedule = await healthApi.getVaccineSchedule(animal.id);\n          return {\n            animalId: animal.id,\n            animalTag: animal.tag || `Hayvan ${animal.id.slice(0, 8)}`,\n            animalBreed: animal.breed || 'Bilinmeyen',\n            schedules: Array.isArray(schedule?.vaccine_schedule) ? schedule.vaccine_schedule : []\n          };\n        } catch (err) {\n          console.error(`Error loading vaccine schedule for animal ${animal.id}:`, err);\n          return {\n            animalId: animal.id,\n            animalTag: animal.tag || `Hayvan ${animal.id.slice(0, 8)}`,\n            animalBreed: animal.breed || 'Bilinmeyen',\n            schedules: []\n          };\n        }\n      });\n\n      const allSchedules = await Promise.all(schedulePromises);\n      setVaccineSchedules(Array.isArray(allSchedules) ? allSchedules : []);\n\n    } catch (err) {\n      setError('Aşı verileri yüklenirken hata oluştu');\n      console.error('Error loading vaccine data:', err);\n      setVaccineSchedules([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-100 text-green-800 border-green-200';\n      case 'due': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';\n      case 'pending': return 'bg-blue-100 text-blue-800 border-blue-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed': return CheckCircle;\n      case 'due': return Calendar;\n      case 'overdue': return AlertTriangle;\n      case 'pending': return Clock;\n      default: return Shield;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return 'Tamamlandı';\n      case 'due': return 'Zamanı Geldi';\n      case 'overdue': return 'Gecikmiş';\n      case 'pending': return 'Bekliyor';\n      default: return 'Bilinmiyor';\n    }\n  };\n\n  // Filtreleme\n  const filteredSchedules = vaccineSchedules.filter(animalSchedule => {\n    const matchesSearch = animalSchedule.animalTag.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         animalSchedule.animalBreed.toLowerCase().includes(searchTerm.toLowerCase());\n\n    if (!matchesSearch) return false;\n\n    if (statusFilter === 'all') return true;\n\n    return animalSchedule.schedules.some((schedule: any) => schedule.status === statusFilter);\n  });\n\n  if (loading && vaccineSchedules.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Aşı verileri yükleniyor...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Link\n            href=\"/health\"\n            className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Aşı Yönetimi</h1>\n            <p className=\"text-gray-600 mt-1\">\n              Hayvan aşı takvimleri ve kayıtları\n            </p>\n          </div>\n        </div>\n        <button className=\"btn-primary text-white px-4 py-2 rounded-md\">\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Aşı Kaydı Ekle\n        </button>\n      </div>\n\n      {/* Çiftlik Seçimi ve Filtreler */}\n      <div className=\"content-overlay p-4\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n          <div className=\"flex items-center space-x-4\">\n            <label className=\"text-sm font-medium text-gray-700\">Çiftlik:</label>\n            <select\n              value={selectedFarmId}\n              onChange={(e) => setSelectedFarmId(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            >\n              {farms.map((farm) => (\n                <option key={farm.id} value={farm.id}>\n                  {farm.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Arama */}\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Hayvan ara...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              />\n            </div>\n\n            {/* Durum Filtresi */}\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-4 w-4 text-gray-400\" />\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                <option value=\"all\">Tüm Durumlar</option>\n                <option value=\"overdue\">Gecikmiş</option>\n                <option value=\"due\">Zamanı Geldi</option>\n                <option value=\"pending\">Bekliyor</option>\n                <option value=\"completed\">Tamamlandı</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <p className=\"text-red-800 text-sm\">{error}</p>\n        </div>\n      )}\n\n      {/* Aşı Takvimleri */}\n      {filteredSchedules.length === 0 ? (\n        <div className=\"content-overlay p-8 text-center\">\n          <Shield className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Aşı kaydı bulunamadı</h3>\n          <p className=\"text-gray-600 mb-4\">\n            {searchTerm || statusFilter !== 'all'\n              ? 'Arama kriterlerinize uygun aşı kaydı bulunamadı.'\n              : 'Bu çiftlikte henüz aşı kaydı bulunmuyor.'\n            }\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          {filteredSchedules.map((animalSchedule) => (\n            <div key={animalSchedule.animalId} className=\"content-overlay p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900\">{animalSchedule.animalTag}</h3>\n                  <p className=\"text-gray-600\">{animalSchedule.animalBreed}</p>\n                </div>\n                <Link\n                  href={`/animals/${animalSchedule.animalId}`}\n                  className=\"text-green-600 hover:text-green-900 text-sm font-medium\"\n                >\n                  Hayvan Detayı →\n                </Link>\n              </div>\n\n              {animalSchedule.schedules.length === 0 ? (\n                <div className=\"text-center py-4\">\n                  <p className=\"text-gray-500\">Bu hayvan için aşı takvimi bulunamadı.</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {animalSchedule.schedules.map((schedule: any, index: number) => {\n                    const StatusIcon = getStatusIcon(schedule.status);\n\n                    return (\n                      <div\n                        key={index}\n                        className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\"\n                      >\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium text-gray-900\">{schedule.vaccine_name}</h4>\n                          <StatusIcon className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n\n                        <div className=\"space-y-2 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-600\">Yaş Aralığı:</span>\n                            <span className=\"font-medium\">\n                              {schedule.age_months_min}\n                              {schedule.age_months_max ? `-${schedule.age_months_max}` : '+'} ay\n                            </span>\n                          </div>\n\n                          {schedule.due_date && (\n                            <div className=\"flex justify-between\">\n                              <span className=\"text-gray-600\">Vade Tarihi:</span>\n                              <span className=\"font-medium\">\n                                {new Date(schedule.due_date).toLocaleDateString('tr-TR')}\n                              </span>\n                            </div>\n                          )}\n\n                          {schedule.cost_estimate && (\n                            <div className=\"flex justify-between\">\n                              <span className=\"text-gray-600\">Tahmini Maliyet:</span>\n                              <span className=\"font-medium\">{schedule.cost_estimate} ₺</span>\n                            </div>\n                          )}\n                        </div>\n\n                        <div className=\"mt-3 flex items-center justify-between\">\n                          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${\n                            getStatusColor(schedule.status)\n                          }`}>\n                            {getStatusText(schedule.status)}\n                          </span>\n\n                          {schedule.is_mandatory && (\n                            <span className=\"px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200\">\n                              Zorunlu\n                            </span>\n                          )}\n                        </div>\n\n                        {schedule.description && (\n                          <p className=\"mt-2 text-xs text-gray-600\">{schedule.description}</p>\n                        )}\n                      </div>\n                    );\n                  })}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB;QACF;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,sHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,CAAC,kBAAkB,UAAU,MAAM,GAAG,GAAG;gBAC3C,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,WAAW;YAEX,MAAM,cAAc,MAAM,sHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YACrD,MAAM,kBAAkB,MAAM,OAAO,CAAC,eAAe,cAAc,EAAE;YACrE,WAAW;YAEX,sCAAsC;YACtC,MAAM,mBAAmB,gBAAgB,GAAG,CAAC,OAAO;gBAClD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;oBACzB,OAAO;wBACL,UAAU;wBACV,WAAW;wBACX,aAAa;wBACb,WAAW,EAAE;oBACf;gBACF;gBAEA,IAAI;oBACF,MAAM,WAAW,MAAM,sHAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE;oBAC7D,OAAO;wBACL,UAAU,OAAO,EAAE;wBACnB,WAAW,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI;wBAC1D,aAAa,OAAO,KAAK,IAAI;wBAC7B,WAAW,MAAM,OAAO,CAAC,UAAU,oBAAoB,SAAS,gBAAgB,GAAG,EAAE;oBACvF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;oBACzE,OAAO;wBACL,UAAU,OAAO,EAAE;wBACnB,WAAW,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI;wBAC1D,aAAa,OAAO,KAAK,IAAI;wBAC7B,WAAW,EAAE;oBACf;gBACF;YACF;YAEA,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;YACvC,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;QAErE,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,oBAAoB,EAAE;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO,2NAAA,CAAA,cAAW;YACpC,KAAK;gBAAO,OAAO,0MAAA,CAAA,WAAQ;YAC3B,KAAK;gBAAW,OAAO,wNAAA,CAAA,gBAAa;YACpC,KAAK;gBAAW,OAAO,oMAAA,CAAA,QAAK;YAC5B;gBAAS,OAAO,sMAAA,CAAA,SAAM;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,aAAa;IACb,MAAM,oBAAoB,iBAAiB,MAAM,CAAC,CAAA;QAChD,MAAM,gBAAgB,eAAe,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvE,eAAe,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE7F,IAAI,CAAC,eAAe,OAAO;QAE3B,IAAI,iBAAiB,OAAO,OAAO;QAEnC,OAAO,eAAe,SAAS,CAAC,IAAI,CAAC,CAAC,WAAkB,SAAS,MAAM,KAAK;IAC9E;IAEA,IAAI,WAAW,iBAAiB,MAAM,KAAK,GAAG;QAC5C,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;kCAKtC,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAoC;;;;;;8CACrD,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;8CAET,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAAqB,OAAO,KAAK,EAAE;sDACjC,KAAK,IAAI;2CADC,KAAK,EAAE;;;;;;;;;;;;;;;;sCAO1B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;YAKxC,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCACV,cAAc,iBAAiB,QAC5B,qDACA;;;;;;;;;;;qCAKR,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,+BACtB,8OAAC;wBAAkC,WAAU;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqC,eAAe,SAAS;;;;;;0DAC3E,8OAAC;gDAAE,WAAU;0DAAiB,eAAe,WAAW;;;;;;;;;;;;kDAE1D,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,SAAS,EAAE,eAAe,QAAQ,EAAE;wCAC3C,WAAU;kDACX;;;;;;;;;;;;4BAKF,eAAe,SAAS,CAAC,MAAM,KAAK,kBACnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;qDAG/B,8OAAC;gCAAI,WAAU;0CACZ,eAAe,SAAS,CAAC,GAAG,CAAC,CAAC,UAAe;oCAC5C,MAAM,aAAa,cAAc,SAAS,MAAM;oCAEhD,qBACE,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6B,SAAS,YAAY;;;;;;kEAChE,8OAAC;wDAAW,WAAU;;;;;;;;;;;;0DAGxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEACb,SAAS,cAAc;oEACvB,SAAS,cAAc,GAAG,CAAC,CAAC,EAAE,SAAS,cAAc,EAAE,GAAG;oEAAI;;;;;;;;;;;;;oDAIlE,SAAS,QAAQ,kBAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,SAAS,QAAQ,EAAE,kBAAkB,CAAC;;;;;;;;;;;;oDAKrD,SAAS,aAAa,kBACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAAe,SAAS,aAAa;oEAAC;;;;;;;;;;;;;;;;;;;0DAK5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,kDAAkD,EAClE,eAAe,SAAS,MAAM,GAC9B;kEACC,cAAc,SAAS,MAAM;;;;;;oDAG/B,SAAS,YAAY,kBACpB,8OAAC;wDAAK,WAAU;kEAA8F;;;;;;;;;;;;4CAMjH,SAAS,WAAW,kBACnB,8OAAC;gDAAE,WAAU;0DAA8B,SAAS,WAAW;;;;;;;uCAjD5D;;;;;gCAqDX;;;;;;;uBA9EI,eAAe,QAAQ;;;;;;;;;;;;;;;;AAuF7C", "debugId": null}}]}