'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ArrowLeft,
  Shield, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Plus,
  Search,
  Filter
} from 'lucide-react';
import { farmApi, animalApi, healthApi } from '@/services/api';
import { Farm, Animal } from '@/types';

export default function VaccineManagementPage() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('');
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [vaccineSchedules, setVaccineSchedules] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadFarmVaccineData();
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (!selectedFarmId && farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    }
  };

  const loadFarmVaccineData = async () => {
    try {
      setLoading(true);
      
      const animalsData = await animalApi.getAnimalsByFarm(selectedFarmId);
      setAnimals(animalsData);
      
      // Her hayvan için aşı takvimini yükle
      const schedulePromises = animalsData.map(async (animal) => {
        try {
          const schedule = await healthApi.getVaccineSchedule(animal.id);
          return {
            animalId: animal.id,
            animalTag: animal.tag || `Hayvan ${animal.id.slice(0, 8)}`,
            animalBreed: animal.breed,
            schedules: schedule
          };
        } catch (err) {
          console.error(`Error loading vaccine schedule for animal ${animal.id}:`, err);
          return {
            animalId: animal.id,
            animalTag: animal.tag || `Hayvan ${animal.id.slice(0, 8)}`,
            animalBreed: animal.breed,
            schedules: []
          };
        }
      });
      
      const allSchedules = await Promise.all(schedulePromises);
      setVaccineSchedules(allSchedules);
      
    } catch (err) {
      setError('Aşı verileri yüklenirken hata oluştu');
      console.error('Error loading vaccine data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'due': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'due': return Calendar;
      case 'overdue': return AlertTriangle;
      case 'pending': return Clock;
      default: return Shield;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Tamamlandı';
      case 'due': return 'Zamanı Geldi';
      case 'overdue': return 'Gecikmiş';
      case 'pending': return 'Bekliyor';
      default: return 'Bilinmiyor';
    }
  };

  // Filtreleme
  const filteredSchedules = vaccineSchedules.filter(animalSchedule => {
    const matchesSearch = animalSchedule.animalTag.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         animalSchedule.animalBreed.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (!matchesSearch) return false;
    
    if (statusFilter === 'all') return true;
    
    return animalSchedule.schedules.some((schedule: any) => schedule.status === statusFilter);
  });

  if (loading && vaccineSchedules.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Aşı verileri yükleniyor...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/health"
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Aşı Yönetimi</h1>
            <p className="text-gray-600 mt-1">
              Hayvan aşı takvimleri ve kayıtları
            </p>
          </div>
        </div>
        <button className="btn-primary text-white px-4 py-2 rounded-md">
          <Plus className="h-4 w-4 mr-2" />
          Aşı Kaydı Ekle
        </button>
      </div>

      {/* Çiftlik Seçimi ve Filtreler */}
      <div className="content-overlay p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Çiftlik:</label>
            <select
              value={selectedFarmId}
              onChange={(e) => setSelectedFarmId(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {farms.map((farm) => (
                <option key={farm.id} value={farm.id}>
                  {farm.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-4">
            {/* Arama */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Hayvan ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Durum Filtresi */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="all">Tüm Durumlar</option>
                <option value="overdue">Gecikmiş</option>
                <option value="due">Zamanı Geldi</option>
                <option value="pending">Bekliyor</option>
                <option value="completed">Tamamlandı</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Aşı Takvimleri */}
      {filteredSchedules.length === 0 ? (
        <div className="content-overlay p-8 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aşı kaydı bulunamadı</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'all' 
              ? 'Arama kriterlerinize uygun aşı kaydı bulunamadı.'
              : 'Bu çiftlikte henüz aşı kaydı bulunmuyor.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredSchedules.map((animalSchedule) => (
            <div key={animalSchedule.animalId} className="content-overlay p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{animalSchedule.animalTag}</h3>
                  <p className="text-gray-600">{animalSchedule.animalBreed}</p>
                </div>
                <Link
                  href={`/animals/${animalSchedule.animalId}`}
                  className="text-green-600 hover:text-green-900 text-sm font-medium"
                >
                  Hayvan Detayı →
                </Link>
              </div>

              {animalSchedule.schedules.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-gray-500">Bu hayvan için aşı takvimi bulunamadı.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {animalSchedule.schedules.map((schedule: any, index: number) => {
                    const StatusIcon = getStatusIcon(schedule.status);
                    
                    return (
                      <div
                        key={index}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{schedule.vaccine_name}</h4>
                          <StatusIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Yaş Aralığı:</span>
                            <span className="font-medium">
                              {schedule.age_months_min}
                              {schedule.age_months_max ? `-${schedule.age_months_max}` : '+'} ay
                            </span>
                          </div>
                          
                          {schedule.due_date && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Vade Tarihi:</span>
                              <span className="font-medium">
                                {new Date(schedule.due_date).toLocaleDateString('tr-TR')}
                              </span>
                            </div>
                          )}
                          
                          {schedule.cost_estimate && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Tahmini Maliyet:</span>
                              <span className="font-medium">{schedule.cost_estimate} ₺</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="mt-3 flex items-center justify-between">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${
                            getStatusColor(schedule.status)
                          }`}>
                            {getStatusText(schedule.status)}
                          </span>
                          
                          {schedule.is_mandatory && (
                            <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                              Zorunlu
                            </span>
                          )}
                        </div>
                        
                        {schedule.description && (
                          <p className="mt-2 text-xs text-gray-600">{schedule.description}</p>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
