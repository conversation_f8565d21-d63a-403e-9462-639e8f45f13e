{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Tüm hatırlatmaları getir\n  getAllReminders: async (): Promise<any[]> => {\n    const response = await api.get('/reminders/all');\n    return response.data;\n  },\n\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Hatırlatma sil\n  deleteReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.delete(`/reminders/${reminderId}`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/reminders/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  Bell,\n  Calendar,\n  Shield,\n  Stethoscope,\n  Heart,\n  Wheat,\n  Users,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  Plus,\n  Search,\n  Filter,\n  Edit,\n  Trash2\n} from 'lucide-react';\nimport { farmApi, reminderApi } from '@/services/api';\nimport { Farm } from '@/types';\n\nexport default function RemindersPage() {\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('all');\n  const [reminders, setReminders] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [typeFilter, setTypeFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    loadReminders();\n  }, [selectedFarmId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n    } catch (err) {\n      setError('Çiftlik verileri yüklenirken hata oluştu');\n      console.error('Error loading farms:', err);\n    }\n  };\n\n  const loadReminders = async () => {\n    try {\n      setLoading(true);\n\n      let remindersData;\n      if (selectedFarmId === 'all') {\n        remindersData = await reminderApi.getAllReminders();\n      } else {\n        remindersData = await reminderApi.getFarmReminders(selectedFarmId);\n      }\n\n      // Güvenli array kontrolü\n      const safeRemindersData = Array.isArray(remindersData) ? remindersData : [];\n      setReminders(safeRemindersData);\n    } catch (err) {\n      setError('Hatırlatmalar yüklenirken hata oluştu');\n      console.error('Error loading reminders:', err);\n      setReminders([]); // Hata durumunda boş array set et\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCompleteReminder = async (reminderId: string) => {\n    try {\n      await reminderApi.completeReminder(reminderId);\n      loadReminders(); // Listeyi yenile\n    } catch (err) {\n      setError('Hatırlatma tamamlanırken hata oluştu');\n      console.error('Error completing reminder:', err);\n    }\n  };\n\n  const handleDeleteReminder = async (reminderId: string) => {\n    if (!window.confirm('Bu hatırlatmayı silmek istediğinizden emin misiniz?')) {\n      return;\n    }\n\n    try {\n      await reminderApi.deleteReminder(reminderId);\n      loadReminders(); // Listeyi yenile\n    } catch (err) {\n      setError('Hatırlatma silinirken hata oluştu');\n      console.error('Error deleting reminder:', err);\n    }\n  };\n\n  const getReminderIcon = (type: string) => {\n    switch (type) {\n      case 'vaccination': return Shield;\n      case 'health_check': return Stethoscope;\n      case 'treatment': return Heart;\n      case 'feeding': return Wheat;\n      case 'breeding': return Users;\n      default: return Bell;\n    }\n  };\n\n  const getReminderTypeText = (type: string) => {\n    switch (type) {\n      case 'vaccination': return 'Aşı';\n      case 'health_check': return 'Sağlık Kontrolü';\n      case 'treatment': return 'Tedavi';\n      case 'feeding': return 'Beslenme';\n      case 'breeding': return 'Üreme';\n      default: return 'Genel';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'bg-red-100 text-red-800 border-red-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'low': return 'bg-green-100 text-green-800 border-green-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getPriorityText = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'Yüksek';\n      case 'medium': return 'Orta';\n      case 'low': return 'Düşük';\n      default: return 'Normal';\n    }\n  };\n\n  const getStatusColor = (reminder: any) => {\n    if (reminder.is_completed) {\n      return 'bg-green-100 text-green-800 border-green-200';\n    }\n\n    const dueDate = new Date(reminder.due_date);\n    const today = new Date();\n\n    if (dueDate < today) {\n      return 'bg-red-100 text-red-800 border-red-200';\n    }\n\n    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n    if (dueDate <= weekFromNow) {\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n    }\n\n    return 'bg-blue-100 text-blue-800 border-blue-200';\n  };\n\n  const getStatusText = (reminder: any) => {\n    if (reminder.is_completed) {\n      return 'Tamamlandı';\n    }\n\n    const dueDate = new Date(reminder.due_date);\n    const today = new Date();\n\n    if (dueDate < today) {\n      return 'Gecikmiş';\n    }\n\n    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n    if (dueDate <= weekFromNow) {\n      return 'Yaklaşıyor';\n    }\n\n    return 'Bekliyor';\n  };\n\n  const getStatusIcon = (reminder: any) => {\n    if (reminder.is_completed) {\n      return CheckCircle;\n    }\n\n    const dueDate = new Date(reminder.due_date);\n    const today = new Date();\n\n    if (dueDate < today) {\n      return AlertTriangle;\n    }\n\n    return Clock;\n  };\n\n  // Filtreleme\n  const filteredReminders = Array.isArray(reminders) ? reminders.filter(reminder => {\n    if (!reminder) return false;\n\n    const matchesSearch = (reminder.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         (reminder.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         (reminder.tag || '').toLowerCase().includes(searchTerm.toLowerCase());\n\n    if (!matchesSearch) return false;\n\n    if (typeFilter !== 'all' && reminder.reminder_type !== typeFilter) return false;\n\n    if (statusFilter === 'completed' && !reminder.is_completed) return false;\n    if (statusFilter === 'pending' && reminder.is_completed) return false;\n    if (statusFilter === 'overdue') {\n      if (!reminder.due_date) return false;\n      const dueDate = new Date(reminder.due_date);\n      const today = new Date();\n      if (reminder.is_completed || dueDate >= today) return false;\n    }\n\n    return true;\n  }) : [];\n\n  // Hatırlatmaları tarihe göre sırala\n  const sortedReminders = filteredReminders.sort((a, b) => {\n    if (!a || !b) return 0;\n\n    if (a.is_completed !== b.is_completed) {\n      return a.is_completed ? 1 : -1; // Tamamlanmayanları üstte göster\n    }\n\n    if (!a.due_date || !b.due_date) return 0;\n    return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();\n  });\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Hatırlatmalar yükleniyor...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Hatırlatmalar</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Tüm hatırlatmaları görüntüleyin ve yönetin\n          </p>\n        </div>\n        <button className=\"btn-primary text-white px-4 py-2 rounded-md\">\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Yeni Hatırlatma\n        </button>\n      </div>\n\n      {/* Filtreler */}\n      <div className=\"content-overlay p-4\">\n        <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n          <div className=\"flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4\">\n            {/* Çiftlik Seçimi */}\n            <div className=\"flex items-center space-x-2\">\n              <label className=\"text-sm font-medium text-gray-700\">Çiftlik:</label>\n              <select\n                value={selectedFarmId}\n                onChange={(e) => setSelectedFarmId(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                <option value=\"all\">Tüm Çiftlikler</option>\n                {farms.map((farm) => (\n                  <option key={farm.id} value={farm.id}>\n                    {farm.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Tür Filtresi */}\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-4 w-4 text-gray-400\" />\n              <select\n                value={typeFilter}\n                onChange={(e) => setTypeFilter(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                <option value=\"all\">Tüm Türler</option>\n                <option value=\"vaccination\">Aşı</option>\n                <option value=\"health_check\">Sağlık Kontrolü</option>\n                <option value=\"treatment\">Tedavi</option>\n                <option value=\"feeding\">Beslenme</option>\n                <option value=\"breeding\">Üreme</option>\n              </select>\n            </div>\n\n            {/* Durum Filtresi */}\n            <div className=\"flex items-center space-x-2\">\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                <option value=\"all\">Tüm Durumlar</option>\n                <option value=\"overdue\">Gecikmiş</option>\n                <option value=\"pending\">Bekliyor</option>\n                <option value=\"completed\">Tamamlandı</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Arama */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Hatırlatma ara...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <p className=\"text-red-800 text-sm\">{error}</p>\n        </div>\n      )}\n\n      {/* Hatırlatma Listesi */}\n      {sortedReminders.length === 0 ? (\n        <div className=\"content-overlay p-8 text-center\">\n          <Bell className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Hatırlatma bulunamadı</h3>\n          <p className=\"text-gray-600 mb-4\">\n            {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'\n              ? 'Arama kriterlerinize uygun hatırlatma bulunamadı.'\n              : 'Henüz hatırlatma oluşturulmamış.'\n            }\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {sortedReminders.map((reminder) => {\n            if (!reminder || !reminder.id) return null;\n\n            const Icon = getReminderIcon(reminder.reminder_type);\n            const StatusIcon = getStatusIcon(reminder);\n\n            return (\n              <div\n                key={reminder.id}\n                className={`content-overlay p-6 hover-glow ${\n                  reminder.is_completed ? 'opacity-75' : ''\n                }`}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start space-x-4 flex-1\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <Icon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3 mb-2\">\n                        <h3 className=\"text-lg font-medium text-gray-900\">{reminder.title || 'Başlıksız'}</h3>\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${\n                          getStatusColor(reminder)\n                        }`}>\n                          {getStatusText(reminder)}\n                        </span>\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${\n                          getPriorityColor(reminder.priority)\n                        }`}>\n                          {getPriorityText(reminder.priority)}\n                        </span>\n                      </div>\n\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-2\">\n                        <span>{getReminderTypeText(reminder.reminder_type)}</span>\n                        <span>•</span>\n                        <span>{reminder.tag || 'Etiket yok'}</span>\n                        <span>•</span>\n                        <span>{reminder.due_date ? new Date(reminder.due_date).toLocaleDateString('tr-TR') : 'Tarih yok'}</span>\n                      </div>\n\n                      {reminder.description && (\n                        <p className=\"text-gray-600 text-sm\">{reminder.description}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    {!reminder.is_completed && (\n                      <button\n                        onClick={() => handleCompleteReminder(reminder.id)}\n                        className=\"p-2 text-green-600 hover:bg-green-50 rounded-md transition-colors\"\n                        title=\"Tamamla\"\n                      >\n                        <CheckCircle className=\"h-5 w-5\" />\n                      </button>\n                    )}\n\n                    <button\n                      className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors\"\n                      title=\"Düzenle\"\n                    >\n                      <Edit className=\"h-5 w-5\" />\n                    </button>\n\n                    <button\n                      onClick={() => handleDeleteReminder(reminder.id)}\n                      className=\"p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors\"\n                      title=\"Sil\"\n                    >\n                      <Trash2 className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AArBA;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,sHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YAEX,IAAI;YACJ,IAAI,mBAAmB,OAAO;gBAC5B,gBAAgB,MAAM,sHAAA,CAAA,cAAW,CAAC,eAAe;YACnD,OAAO;gBACL,gBAAgB,MAAM,sHAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC;YACrD;YAEA,yBAAyB;YACzB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;YAC3E,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,aAAa,EAAE,GAAG,kCAAkC;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,sHAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC;YACnC,iBAAiB,iBAAiB;QACpC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,OAAO,OAAO,CAAC,wDAAwD;YAC1E;QACF;QAEA,IAAI;YACF,MAAM,sHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YACjC,iBAAiB,iBAAiB;QACpC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAe,OAAO,sMAAA,CAAA,SAAM;YACjC,KAAK;gBAAgB,OAAO,gNAAA,CAAA,cAAW;YACvC,KAAK;gBAAa,OAAO,oMAAA,CAAA,QAAK;YAC9B,KAAK;gBAAW,OAAO,oMAAA,CAAA,QAAK;YAC5B,KAAK;gBAAY,OAAO,oMAAA,CAAA,QAAK;YAC7B;gBAAS,OAAO,kMAAA,CAAA,OAAI;QACtB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,YAAY,EAAE;YACzB,OAAO;QACT;QAEA,MAAM,UAAU,IAAI,KAAK,SAAS,QAAQ;QAC1C,MAAM,QAAQ,IAAI;QAElB,IAAI,UAAU,OAAO;YACnB,OAAO;QACT;QAEA,MAAM,cAAc,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAClE,IAAI,WAAW,aAAa;YAC1B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,YAAY,EAAE;YACzB,OAAO;QACT;QAEA,MAAM,UAAU,IAAI,KAAK,SAAS,QAAQ;QAC1C,MAAM,QAAQ,IAAI;QAElB,IAAI,UAAU,OAAO;YACnB,OAAO;QACT;QAEA,MAAM,cAAc,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAClE,IAAI,WAAW,aAAa;YAC1B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,YAAY,EAAE;YACzB,OAAO,2NAAA,CAAA,cAAW;QACpB;QAEA,MAAM,UAAU,IAAI,KAAK,SAAS,QAAQ;QAC1C,MAAM,QAAQ,IAAI;QAElB,IAAI,UAAU,OAAO;YACnB,OAAO,wNAAA,CAAA,gBAAa;QACtB;QAEA,OAAO,oMAAA,CAAA,QAAK;IACd;IAEA,aAAa;IACb,MAAM,oBAAoB,MAAM,OAAO,CAAC,aAAa,UAAU,MAAM,CAAC,CAAA;QACpE,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,gBAAgB,CAAC,SAAS,KAAK,IAAI,EAAE,EAAE,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACrE,CAAC,SAAS,WAAW,IAAI,EAAE,EAAE,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1E,CAAC,SAAS,GAAG,IAAI,EAAE,EAAE,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEvF,IAAI,CAAC,eAAe,OAAO;QAE3B,IAAI,eAAe,SAAS,SAAS,aAAa,KAAK,YAAY,OAAO;QAE1E,IAAI,iBAAiB,eAAe,CAAC,SAAS,YAAY,EAAE,OAAO;QACnE,IAAI,iBAAiB,aAAa,SAAS,YAAY,EAAE,OAAO;QAChE,IAAI,iBAAiB,WAAW;YAC9B,IAAI,CAAC,SAAS,QAAQ,EAAE,OAAO;YAC/B,MAAM,UAAU,IAAI,KAAK,SAAS,QAAQ;YAC1C,MAAM,QAAQ,IAAI;YAClB,IAAI,SAAS,YAAY,IAAI,WAAW,OAAO,OAAO;QACxD;QAEA,OAAO;IACT,KAAK,EAAE;IAEP,oCAAoC;IACpC,MAAM,kBAAkB,kBAAkB,IAAI,CAAC,CAAC,GAAG;QACjD,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO;QAErB,IAAI,EAAE,YAAY,KAAK,EAAE,YAAY,EAAE;YACrC,OAAO,EAAE,YAAY,GAAG,IAAI,CAAC,GAAG,iCAAiC;QACnE;QAEA,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO;QACvC,OAAO,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;IACtE;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDACrD,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;gDACnB,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wDAAqB,OAAO,KAAK,EAAE;kEACjC,KAAK,IAAI;uDADC,KAAK,EAAE;;;;;;;;;;;;;;;;;8CAQ1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;;8CAK7B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOjB,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;YAKxC,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCACV,cAAc,eAAe,SAAS,iBAAiB,QACpD,sDACA;;;;;;;;;;;qCAKR,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC;oBACpB,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,OAAO;oBAEtC,MAAM,OAAO,gBAAgB,SAAS,aAAa;oBACnD,MAAM,aAAa,cAAc;oBAEjC,qBACE,8OAAC;wBAEC,WAAW,CAAC,+BAA+B,EACzC,SAAS,YAAY,GAAG,eAAe,IACvC;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC,SAAS,KAAK,IAAI;;;;;;sEACrE,8OAAC;4DAAK,WAAW,CAAC,kDAAkD,EAClE,eAAe,WACf;sEACC,cAAc;;;;;;sEAEjB,8OAAC;4DAAK,WAAW,CAAC,kDAAkD,EAClE,iBAAiB,SAAS,QAAQ,GAClC;sEACC,gBAAgB,SAAS,QAAQ;;;;;;;;;;;;8DAItC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,oBAAoB,SAAS,aAAa;;;;;;sEACjD,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,SAAS,GAAG,IAAI;;;;;;sEACvB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,SAAS,QAAQ,GAAG,IAAI,KAAK,SAAS,QAAQ,EAAE,kBAAkB,CAAC,WAAW;;;;;;;;;;;;gDAGtF,SAAS,WAAW,kBACnB,8OAAC;oDAAE,WAAU;8DAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;8CAKhE,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,SAAS,YAAY,kBACrB,8OAAC;4CACC,SAAS,IAAM,uBAAuB,SAAS,EAAE;4CACjD,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAI3B,8OAAC;4CACC,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,8OAAC;4CACC,SAAS,IAAM,qBAAqB,SAAS,EAAE;4CAC/C,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBA/DnB,SAAS,EAAE;;;;;gBAqEtB;;;;;;;;;;;;AAKV", "debugId": null}}]}