'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Bell,
  Calendar,
  Shield,
  Stethoscope,
  Heart,
  Wheat,
  Users,
  CheckCircle,
  Clock,
  AlertTriangle,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2
} from 'lucide-react';
import { farmApi, reminderApi } from '@/services/api';
import { Farm } from '@/types';

export default function RemindersPage() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('all');
  const [reminders, setReminders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    loadReminders();
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    }
  };

  const loadReminders = async () => {
    try {
      setLoading(true);

      let remindersData;
      if (selectedFarmId === 'all') {
        remindersData = await reminderApi.getAllReminders();
      } else {
        const farmRemindersData = await reminderApi.getFarmReminders(selectedFarmId);
        // Farm reminders kategorilere ayrılmış geliyor, bunları birleştir
        if (farmRemindersData && typeof farmRemindersData === 'object') {
          remindersData = [
            ...(Array.isArray(farmRemindersData.overdue) ? farmRemindersData.overdue : []),
            ...(Array.isArray(farmRemindersData.today) ? farmRemindersData.today : []),
            ...(Array.isArray(farmRemindersData.upcoming) ? farmRemindersData.upcoming : [])
          ];
        } else {
          remindersData = [];
        }
      }

      // Güvenli array kontrolü
      const safeRemindersData = Array.isArray(remindersData) ? remindersData : [];
      setReminders(safeRemindersData);
    } catch (err) {
      setError('Hatırlatmalar yüklenirken hata oluştu');
      console.error('Error loading reminders:', err);
      setReminders([]); // Hata durumunda boş array set et
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteReminder = async (reminderId: string) => {
    try {
      await reminderApi.completeReminder(reminderId);
      loadReminders(); // Listeyi yenile
    } catch (err) {
      setError('Hatırlatma tamamlanırken hata oluştu');
      console.error('Error completing reminder:', err);
    }
  };

  const handleDeleteReminder = async (reminderId: string) => {
    if (!window.confirm('Bu hatırlatmayı silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      await reminderApi.deleteReminder(reminderId);
      loadReminders(); // Listeyi yenile
    } catch (err) {
      setError('Hatırlatma silinirken hata oluştu');
      console.error('Error deleting reminder:', err);
    }
  };

  const getReminderIcon = (type: string) => {
    switch (type) {
      case 'vaccination': return Shield;
      case 'health_check': return Stethoscope;
      case 'treatment': return Heart;
      case 'feeding': return Wheat;
      case 'breeding': return Users;
      default: return Bell;
    }
  };

  const getReminderTypeText = (type: string) => {
    switch (type) {
      case 'vaccination': return 'Aşı';
      case 'health_check': return 'Sağlık Kontrolü';
      case 'treatment': return 'Tedavi';
      case 'feeding': return 'Beslenme';
      case 'breeding': return 'Üreme';
      default: return 'Genel';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return 'Yüksek';
      case 'medium': return 'Orta';
      case 'low': return 'Düşük';
      default: return 'Normal';
    }
  };

  const getStatusColor = (reminder: any) => {
    if (reminder.is_completed) {
      return 'bg-green-100 text-green-800 border-green-200';
    }

    const dueDate = new Date(reminder.due_date);
    const today = new Date();

    if (dueDate < today) {
      return 'bg-red-100 text-red-800 border-red-200';
    }

    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    if (dueDate <= weekFromNow) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }

    return 'bg-blue-100 text-blue-800 border-blue-200';
  };

  const getStatusText = (reminder: any) => {
    if (reminder.is_completed) {
      return 'Tamamlandı';
    }

    const dueDate = new Date(reminder.due_date);
    const today = new Date();

    if (dueDate < today) {
      return 'Gecikmiş';
    }

    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    if (dueDate <= weekFromNow) {
      return 'Yaklaşıyor';
    }

    return 'Bekliyor';
  };

  const getStatusIcon = (reminder: any) => {
    if (reminder.is_completed) {
      return CheckCircle;
    }

    const dueDate = new Date(reminder.due_date);
    const today = new Date();

    if (dueDate < today) {
      return AlertTriangle;
    }

    return Clock;
  };

  // Filtreleme
  const filteredReminders = Array.isArray(reminders) ? reminders.filter(reminder => {
    if (!reminder) return false;

    const matchesSearch = (reminder.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (reminder.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (reminder.tag || '').toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    if (typeFilter !== 'all' && reminder.reminder_type !== typeFilter) return false;

    if (statusFilter === 'completed' && !reminder.is_completed) return false;
    if (statusFilter === 'pending' && reminder.is_completed) return false;
    if (statusFilter === 'overdue') {
      if (!reminder.due_date) return false;
      const dueDate = new Date(reminder.due_date);
      const today = new Date();
      if (reminder.is_completed || dueDate >= today) return false;
    }

    return true;
  }) : [];

  // Hatırlatmaları tarihe göre sırala
  const sortedReminders = filteredReminders.sort((a, b) => {
    if (!a || !b) return 0;

    if (a.is_completed !== b.is_completed) {
      return a.is_completed ? 1 : -1; // Tamamlanmayanları üstte göster
    }

    if (!a.due_date || !b.due_date) return 0;
    return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
  });

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Hatırlatmalar yükleniyor...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Hatırlatmalar</h1>
          <p className="text-gray-600 mt-1">
            Tüm hatırlatmaları görüntüleyin ve yönetin
          </p>
        </div>
        <button className="btn-primary text-white px-4 py-2 rounded-md">
          <Plus className="h-4 w-4 mr-2" />
          Yeni Hatırlatma
        </button>
      </div>

      {/* Filtreler */}
      <div className="content-overlay p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
            {/* Çiftlik Seçimi */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Çiftlik:</label>
              <select
                value={selectedFarmId}
                onChange={(e) => setSelectedFarmId(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="all">Tüm Çiftlikler</option>
                {farms.map((farm) => (
                  <option key={farm.id} value={farm.id}>
                    {farm.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Tür Filtresi */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="all">Tüm Türler</option>
                <option value="vaccination">Aşı</option>
                <option value="health_check">Sağlık Kontrolü</option>
                <option value="treatment">Tedavi</option>
                <option value="feeding">Beslenme</option>
                <option value="breeding">Üreme</option>
              </select>
            </div>

            {/* Durum Filtresi */}
            <div className="flex items-center space-x-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="all">Tüm Durumlar</option>
                <option value="overdue">Gecikmiş</option>
                <option value="pending">Bekliyor</option>
                <option value="completed">Tamamlandı</option>
              </select>
            </div>
          </div>

          {/* Arama */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Hatırlatma ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Hatırlatma Listesi */}
      {sortedReminders.length === 0 ? (
        <div className="content-overlay p-8 text-center">
          <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Hatırlatma bulunamadı</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
              ? 'Arama kriterlerinize uygun hatırlatma bulunamadı.'
              : 'Henüz hatırlatma oluşturulmamış.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {sortedReminders.map((reminder) => {
            if (!reminder || !reminder.id) return null;

            const Icon = getReminderIcon(reminder.reminder_type);
            const StatusIcon = getStatusIcon(reminder);

            return (
              <div
                key={reminder.id}
                className={`content-overlay p-6 hover-glow ${
                  reminder.is_completed ? 'opacity-75' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{reminder.title || 'Başlıksız'}</h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${
                          getStatusColor(reminder)
                        }`}>
                          {getStatusText(reminder)}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${
                          getPriorityColor(reminder.priority)
                        }`}>
                          {getPriorityText(reminder.priority)}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <span>{getReminderTypeText(reminder.reminder_type)}</span>
                        <span>•</span>
                        <span>{reminder.tag || 'Etiket yok'}</span>
                        <span>•</span>
                        <span>{reminder.due_date ? new Date(reminder.due_date).toLocaleDateString('tr-TR') : 'Tarih yok'}</span>
                      </div>

                      {reminder.description && (
                        <p className="text-gray-600 text-sm">{reminder.description}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {!reminder.is_completed && (
                      <button
                        onClick={() => handleCompleteReminder(reminder.id)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-md transition-colors"
                        title="Tamamla"
                      >
                        <CheckCircle className="h-5 w-5" />
                      </button>
                    )}

                    <button
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                      title="Düzenle"
                    >
                      <Edit className="h-5 w-5" />
                    </button>

                    <button
                      onClick={() => handleDeleteReminder(reminder.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                      title="Sil"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
