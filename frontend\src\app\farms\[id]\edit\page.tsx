'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, Building2 } from 'lucide-react';
import { farmApi } from '@/services/api';
import { Farm } from '@/types';

interface FarmUpdate {
  name: string;
  location: string;
  total_land_hectares: number;
  pasture_land_hectares: number;
  barn_capacity: number;
  feed_storage_capacity_tons: number;
  silage_capacity_tons: number;
  hay_storage_capacity_tons: number;
  water_storage_capacity_liters: number;
  milking_parlor_capacity: number;
  quarantine_facility_capacity: number;
  hospital_pen_capacity: number;
  handling_facility_present: boolean;
  scale_capacity_kg: number;
}

export default function EditFarmPage() {
  const params = useParams();
  const router = useRouter();
  const farmId = params.id as string;

  const [farm, setFarm] = useState<Farm | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<FarmUpdate>({
    name: '',
    location: '',
    total_land_hectares: 0,
    pasture_land_hectares: 0,
    barn_capacity: 0,
    feed_storage_capacity_tons: 0,
    silage_capacity_tons: 0,
    hay_storage_capacity_tons: 0,
    water_storage_capacity_liters: 0,
    milking_parlor_capacity: 0,
    quarantine_facility_capacity: 0,
    hospital_pen_capacity: 0,
    handling_facility_present: false,
    scale_capacity_kg: 0,
  });

  useEffect(() => {
    loadFarmData();
  }, [farmId]);

  const loadFarmData = async () => {
    try {
      setLoadingData(true);
      const farmData = await farmApi.getFarm(farmId);
      setFarm(farmData);

      // Form verilerini doldur
      setFormData({
        name: farmData.name,
        location: farmData.location,
        total_land_hectares: farmData.total_land_hectares || 0,
        pasture_land_hectares: farmData.pasture_land_hectares || 0,
        barn_capacity: farmData.barn_capacity || 0,
        feed_storage_capacity_tons: farmData.feed_storage_capacity_tons || 0,
        silage_capacity_tons: farmData.silage_capacity_tons || 0,
        hay_storage_capacity_tons: farmData.hay_storage_capacity_tons || 0,
        water_storage_capacity_liters: farmData.water_storage_capacity_liters || 0,
        milking_parlor_capacity: farmData.milking_parlor_capacity || 0,
        quarantine_facility_capacity: farmData.quarantine_facility_capacity || 0,
        hospital_pen_capacity: farmData.hospital_pen_capacity || 0,
        handling_facility_present: farmData.handling_facility_present || false,
        scale_capacity_kg: farmData.scale_capacity_kg || 0,
      });
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Çiftlik verileri yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoadingData(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 :
              type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.location.trim()) {
      setError('Çiftlik adı ve konum zorunludur');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await farmApi.updateFarm(farmId, formData);
      router.push(`/farms/${farmId}`);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Çiftlik güncellenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Çiftlik verileri yükleniyor...</p>
      </div>
    );
  }

  if (!farm) {
    return (
      <div className="text-center py-12">
        <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Çiftlik bulunamadı
        </h3>
        <p className="text-gray-600 mb-6">
          Aradığınız çiftlik mevcut değil
        </p>
        <Link
          href="/farms"
          className="btn-primary text-white px-6 py-3 rounded-md"
        >
          Çiftlik Listesine Dön
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href={`/farms/${farmId}`}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Çiftlik Düzenle</h1>
          <p className="text-gray-600 mt-1">
            {farm.name} çiftliğinin bilgilerini güncelleyin
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Temel Bilgiler */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Temel Bilgiler</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Çiftlik Adı *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Çiftlik adını girin"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Konum *
              </label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Şehir, İlçe"
              />
            </div>
          </div>
        </div>

        {/* Arazi Bilgileri */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Arazi Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Toplam Arazi (Hektar)
              </label>
              <input
                type="number"
                name="total_land_hectares"
                value={formData.total_land_hectares}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mera Alanı (Hektar)
              </label>
              <input
                type="number"
                name="pasture_land_hectares"
                value={formData.pasture_land_hectares}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Tesis Kapasiteleri */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Tesis Kapasiteleri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ahır Kapasitesi (Hayvan)
              </label>
              <input
                type="number"
                name="barn_capacity"
                value={formData.barn_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sağım Salonu Kapasitesi
              </label>
              <input
                type="number"
                name="milking_parlor_capacity"
                value={formData.milking_parlor_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Terazi Kapasitesi (kg)
              </label>
              <input
                type="number"
                name="scale_capacity_kg"
                value={formData.scale_capacity_kg}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Depolama Kapasiteleri */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Depolama Kapasiteleri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yem Depolama (Ton)
              </label>
              <input
                type="number"
                name="feed_storage_capacity_tons"
                value={formData.feed_storage_capacity_tons}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Silaj Kapasitesi (Ton)
              </label>
              <input
                type="number"
                name="silage_capacity_tons"
                value={formData.silage_capacity_tons}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Saman Depolama (Ton)
              </label>
              <input
                type="number"
                name="hay_storage_capacity_tons"
                value={formData.hay_storage_capacity_tons}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Su Depolama (Litre)
              </label>
              <input
                type="number"
                name="water_storage_capacity_liters"
                value={formData.water_storage_capacity_liters}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Karantina Kapasitesi
              </label>
              <input
                type="number"
                name="quarantine_facility_capacity"
                value={formData.quarantine_facility_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hastane Bölmesi Kapasitesi
              </label>
              <input
                type="number"
                name="hospital_pen_capacity"
                value={formData.hospital_pen_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Özel Tesisler */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Özel Tesisler</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="handling_facility_present"
                checked={formData.handling_facility_present}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Hayvan Yönetim Tesisi Mevcut
              </label>
            </div>
          </div>
        </div>

        {/* Form Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6">
          <Link
            href={`/farms/${farmId}`}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="flex items-center space-x-2 btn-primary text-white px-6 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Güncelleniyor...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Değişiklikleri Kaydet</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
