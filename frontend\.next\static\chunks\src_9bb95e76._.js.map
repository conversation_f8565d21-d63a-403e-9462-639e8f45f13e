{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Tüm hatırlatmaları getir\n  getAllReminders: async (): Promise<any[]> => {\n    const response = await api.get('/reminders/all');\n    return response.data;\n  },\n\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Hatırlatma sil\n  deleteReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.delete(`/reminders/${reminderId}`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/animals/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  Beef, \n  Plus, \n  Edit, \n  Trash2, \n  Calendar,\n  Weight,\n  Heart,\n  Baby\n} from 'lucide-react';\nimport { animalApi, farmApi } from '@/services/api';\nimport { Animal, Farm, AnimalStats } from '@/types';\n\nexport default function AnimalsPage() {\n  const searchParams = useSearchParams();\n  const farmId = searchParams.get('farm');\n  \n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>(farmId || '');\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [stats, setStats] = useState<AnimalStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadAnimals(selectedFarmId);\n      loadStats(selectedFarmId);\n    }\n  }, [selectedFarmId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (!selectedFarmId && farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlik verileri yüklenirken hata oluştu');\n      console.error('Error loading farms:', err);\n    }\n  };\n\n  const loadAnimals = async (farmId: string) => {\n    try {\n      setLoading(true);\n      const animalsData = await animalApi.getAnimalsByFarm(farmId);\n      setAnimals(animalsData);\n    } catch (err) {\n      setError('Hayvan verileri yüklenirken hata oluştu');\n      console.error('Error loading animals:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStats = async (farmId: string) => {\n    try {\n      const statsData = await animalApi.getFarmAnimalStats(farmId);\n      setStats(statsData);\n    } catch (err) {\n      console.error('Error loading stats:', err);\n    }\n  };\n\n  const handleDeleteAnimal = async (animalId: string) => {\n    if (!confirm('Bu hayvanı silmek istediğinizden emin misiniz?')) {\n      return;\n    }\n\n    try {\n      await animalApi.deleteAnimal(animalId);\n      setAnimals(animals.filter(animal => animal.id !== animalId));\n      if (selectedFarmId) {\n        loadStats(selectedFarmId);\n      }\n    } catch (err) {\n      alert('Hayvan silinirken hata oluştu');\n      console.error('Error deleting animal:', err);\n    }\n  };\n\n  const getBreedDisplayName = (breed: string) => {\n    const breedNames: Record<string, string> = {\n      'angus': 'Angus',\n      'hereford': 'Hereford',\n      'simmental': 'Simmental',\n      'charolais': 'Charolais',\n      'limousin': 'Limousin',\n      'holstein': 'Holstein',\n      'brown_swiss': 'Brown Swiss',\n      'native_anatolian': 'Yerli Anadolu',\n      'crossbred': 'Melez'\n    };\n    return breedNames[breed] || breed;\n  };\n\n  const getStatusDisplayName = (status: string) => {\n    const statusNames: Record<string, string> = {\n      'calf': 'Buzağı',\n      'young': 'Genç',\n      'breeding': 'Damızlık',\n      'fattening': 'Besi',\n      'ready_for_sale': 'Satışa Hazır',\n      'sold': 'Satıldı',\n      'dead': 'Öldü'\n    };\n    return statusNames[status] || status;\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'calf': 'bg-blue-100 text-blue-800',\n      'young': 'bg-green-100 text-green-800',\n      'breeding': 'bg-purple-100 text-purple-800',\n      'fattening': 'bg-yellow-100 text-yellow-800',\n      'ready_for_sale': 'bg-orange-100 text-orange-800',\n      'sold': 'bg-gray-100 text-gray-800',\n      'dead': 'bg-red-100 text-red-800'\n    };\n    return colors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (farms.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <Beef className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Önce bir çiftlik oluşturun\n        </h3>\n        <p className=\"text-gray-600 mb-6\">\n          Hayvan eklemek için önce bir çiftlik oluşturmanız gerekiyor\n        </p>\n        <Link\n          href=\"/farms/new\"\n          className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors\"\n        >\n          <Plus className=\"h-5 w-5\" />\n          <span>Çiftlik Oluştur</span>\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Hayvan Yönetimi</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Hayvanlarınızı yönetin ve takip edin\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {farms.length > 1 && (\n            <select\n              value={selectedFarmId}\n              onChange={(e) => setSelectedFarmId(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 bg-white\"\n            >\n              {farms.map((farm) => (\n                <option key={farm.id} value={farm.id}>\n                  {farm.name}\n                </option>\n              ))}\n            </select>\n          )}\n          {selectedFarmId && (\n            <Link\n              href={`/animals/new?farm=${selectedFarmId}`}\n              className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\"\n            >\n              <Plus className=\"h-5 w-5\" />\n              <span>Yeni Hayvan</span>\n            </Link>\n          )}\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      {stats && (\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Toplam Hayvan</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.total_animals}\n                </p>\n              </div>\n              <Beef className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Erkek</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.by_gender.male}\n                </p>\n              </div>\n              <div className=\"h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-blue-600 font-bold\">♂</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Dişi</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.by_gender.female}\n                </p>\n              </div>\n              <div className=\"h-8 w-8 bg-pink-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-pink-600 font-bold\">♀</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Ortalama Ağırlık</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {Math.round(stats.average_weight)} kg\n                </p>\n              </div>\n              <Weight className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Animals List */}\n      {loading ? (\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Hayvanlar yükleniyor...</p>\n          </div>\n        </div>\n      ) : error ? (\n        <div className=\"text-center py-12\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <button \n            onClick={() => selectedFarmId && loadAnimals(selectedFarmId)}\n            className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n          >\n            Tekrar Dene\n          </button>\n        </div>\n      ) : animals.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <Beef className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Henüz hayvan yok\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            İlk hayvanınızı ekleyerek başlayın\n          </p>\n          {selectedFarmId && (\n            <Link\n              href={`/animals/new?farm=${selectedFarmId}`}\n              className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors\"\n            >\n              <Plus className=\"h-5 w-5\" />\n              <span>İlk Hayvanımı Ekle</span>\n            </Link>\n          )}\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Hayvan\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Irk\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Yaş\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Ağırlık\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Durum\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Özel Durum\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    İşlemler\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {animals.map((animal) => (\n                  <tr key={animal.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-10 w-10\">\n                          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${\n                            animal.gender === 'male' ? 'bg-blue-100' : 'bg-pink-100'\n                          }`}>\n                            <span className={`font-bold ${\n                              animal.gender === 'male' ? 'text-blue-600' : 'text-pink-600'\n                            }`}>\n                              {animal.gender === 'male' ? '♂' : '♀'}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            #{animal.id.slice(-8)}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {animal.gender === 'male' ? 'Erkek' : 'Dişi'}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {getBreedDisplayName(animal.breed)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {animal.age_months} ay\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {animal.current_weight_kg} kg\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(animal.status)}`}>\n                        {getStatusDisplayName(animal.status)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {animal.is_pregnant && (\n                        <span className=\"inline-flex items-center space-x-1 text-green-600\">\n                          <Baby className=\"h-4 w-4\" />\n                          <span>Gebe</span>\n                        </span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <Link\n                          href={`/animals/${animal.id}/edit`}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </Link>\n                        <button\n                          onClick={() => handleDeleteAnimal(animal.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAfA;;;;;;AAkBe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,UAAU;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,gBAAgB;gBAClB,YAAY;gBACZ,UAAU;YACZ;QACF;gCAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,yHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,CAAC,kBAAkB,UAAU,MAAM,GAAG,GAAG;gBAC3C,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,WAAW;YACX,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YACrD,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,MAAM,YAAY,MAAM,yHAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC;YACrD,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,mDAAmD;YAC9D;QACF;QAEA,IAAI;YACF,MAAM,yHAAA,CAAA,YAAS,CAAC,YAAY,CAAC;YAC7B,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YAClD,IAAI,gBAAgB;gBAClB,UAAU;YACZ;QACF,EAAE,OAAO,KAAK;YACZ,MAAM;YACN,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,aAAqC;YACzC,SAAS;YACT,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,eAAe;YACf,oBAAoB;YACpB,aAAa;QACf;QACA,OAAO,UAAU,CAAC,MAAM,IAAI;IAC9B;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAsC;YAC1C,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,aAAa;YACb,kBAAkB;YAClB,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,WAAW,CAAC,OAAO,IAAI;IAChC;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,aAAa;YACb,kBAAkB;YAClB,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;8BAChB,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAGzD,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;IAId;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM,GAAG,mBACd,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;0CAET,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAAqB,OAAO,KAAK,EAAE;kDACjC,KAAK,IAAI;uCADC,KAAK,EAAE;;;;;;;;;;4BAMzB,gCACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,kBAAkB,EAAE,gBAAgB;gCAC3C,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAOb,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDACV,MAAM,aAAa;;;;;;;;;;;;8CAGxB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDACV,MAAM,SAAS,CAAC,IAAI;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;kCAKhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDACV,MAAM,SAAS,CAAC,MAAM;;;;;;;;;;;;8CAG3B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;kCAKhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDACV,KAAK,KAAK,CAAC,MAAM,cAAc;gDAAE;;;;;;;;;;;;;8CAGtC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOzB,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;uBAGpC,sBACF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,kBAAkB,YAAY;wBAC7C,WAAU;kCACX;;;;;;;;;;;uBAID,QAAQ,MAAM,KAAK,kBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;oBAGjC,gCACC,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,kBAAkB,EAAE,gBAAgB;wBAC3C,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;qCAKZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAkF;;;;;;;;;;;;;;;;;0CAKpG,6LAAC;gCAAM,WAAU;0CACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAW,CAAC,wDAAwD,EACvE,OAAO,MAAM,KAAK,SAAS,gBAAgB,eAC3C;0EACA,cAAA,6LAAC;oEAAK,WAAW,CAAC,UAAU,EAC1B,OAAO,MAAM,KAAK,SAAS,kBAAkB,iBAC7C;8EACC,OAAO,MAAM,KAAK,SAAS,MAAM;;;;;;;;;;;;;;;;sEAIxC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAoC;wEAC/C,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;8EAErB,6LAAC;oEAAI,WAAU;8EACZ,OAAO,MAAM,KAAK,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;0DAK9C,6LAAC;gDAAG,WAAU;0DACX,oBAAoB,OAAO,KAAK;;;;;;0DAEnC,6LAAC;gDAAG,WAAU;;oDACX,OAAO,UAAU;oDAAC;;;;;;;0DAErB,6LAAC;gDAAG,WAAU;;oDACX,OAAO,iBAAiB;oDAAC;;;;;;;0DAE5B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,OAAO,MAAM,GAAG;8DACzG,qBAAqB,OAAO,MAAM;;;;;;;;;;;0DAGvC,6LAAC;gDAAG,WAAU;0DACX,OAAO,WAAW,kBACjB,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;0DAIZ,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;4DAClC,WAAU;sEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DACC,SAAS,IAAM,mBAAmB,OAAO,EAAE;4DAC3C,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA1DjB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEpC;GAhXwB;;QACD,qIAAA,CAAA,kBAAe;;;KADd", "debugId": null}}]}