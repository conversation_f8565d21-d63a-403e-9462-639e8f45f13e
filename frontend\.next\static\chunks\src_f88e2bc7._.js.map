{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Tüm hatırlatmaları getir\n  getAllReminders: async (): Promise<any[]> => {\n    const response = await api.get('/reminders/all');\n    return response.data;\n  },\n\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Hatırlatma sil\n  deleteReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.delete(`/reminders/${reminderId}`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/farms/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport {\n  ArrowLeft,\n  Edit,\n  Trash2,\n  Building2,\n  MapPin,\n  Calendar,\n  Users,\n  Wheat,\n  Droplets,\n  Scale,\n  Home,\n  AlertCircle,\n  TrendingUp,\n  BarChart3\n} from 'lucide-react';\nimport { farmApi, animalApi, feedApi, simulationApi } from '@/services/api';\nimport { Farm, Animal, Feed } from '@/types';\n\nexport default function FarmDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const farmId = params.id as string;\n\n  const [farm, setFarm] = useState<Farm | null>(null);\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [feeds, setFeeds] = useState<Feed[]>([]);\n  const [simulations, setSimulations] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    loadFarmData();\n  }, [farmId]);\n\n  const loadFarmData = async () => {\n    try {\n      setLoading(true);\n\n      // Paralel olarak tüm verileri yükle\n      const [farmData, animalsData, feedsData, simulationsData] = await Promise.all([\n        farmApi.getFarm(farmId).catch(() => null),\n        animalApi.getAnimalsByFarm(farmId).catch(() => []),\n        feedApi.getFeedsByFarm(farmId).catch(() => []),\n        simulationApi.getSimulationsByFarm(farmId).catch(() => [])\n      ]);\n\n      if (!farmData) {\n        setError('Çiftlik bulunamadı');\n        return;\n      }\n\n      setFarm(farmData);\n      setAnimals(animalsData);\n      setFeeds(feedsData);\n      setSimulations(simulationsData);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || 'Çiftlik verileri yüklenirken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFarm = async () => {\n    if (!farm) return;\n\n    const confirmed = window.confirm(\n      `\"${farm.name}\" çiftliğini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`\n    );\n\n    if (confirmed) {\n      try {\n        await farmApi.deleteFarm(farmId);\n        router.push('/farms');\n      } catch (err: any) {\n        setError(err.response?.data?.detail || 'Çiftlik silinirken hata oluştu');\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Çiftlik verileri yükleniyor...</p>\n      </div>\n    );\n  }\n\n  if (error || !farm) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Çiftlik Bulunamadı</h1>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <AlertCircle className=\"h-5 w-5 text-red-400\" />\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-800\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <Link\n          href=\"/farms\"\n          className=\"btn-primary text-white px-6 py-3 rounded-md inline-flex items-center space-x-2\"\n        >\n          <ArrowLeft className=\"h-4 w-4\" />\n          <span>Çiftlik Listesine Dön</span>\n        </Link>\n      </div>\n    );\n  }\n\n  const tabs = [\n    { id: 'overview', name: 'Genel Bakış', icon: Home },\n    { id: 'animals', name: 'Hayvanlar', icon: Users },\n    { id: 'feeds', name: 'Yemler', icon: Wheat },\n    { id: 'simulations', name: 'Simülasyonlar', icon: TrendingUp },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Link\n            href=\"/farms\"\n            className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">{farm.name}</h1>\n            <div className=\"flex items-center space-x-4 mt-2 text-gray-600\">\n              <div className=\"flex items-center space-x-1\">\n                <MapPin className=\"h-4 w-4\" />\n                <span>{farm.location}</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <Calendar className=\"h-4 w-4\" />\n                <span>Kuruluş: {new Date(farm.established_date).toLocaleDateString('tr-TR')}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href={`/farms/${farmId}/edit`}\n            className=\"flex items-center space-x-2 px-4 py-2 border border-green-600 text-green-600 rounded-md hover:bg-green-50 transition-colors\"\n          >\n            <Edit className=\"h-4 w-4\" />\n            <span>Düzenle</span>\n          </Link>\n          <button\n            onClick={handleDeleteFarm}\n            className=\"flex items-center space-x-2 px-4 py-2 border border-red-600 text-red-600 rounded-md hover:bg-red-50 transition-colors\"\n          >\n            <Trash2 className=\"h-4 w-4\" />\n            <span>Sil</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"content-overlay\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-green-500 text-green-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{tab.name}</span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'overview' && (\n        <div className=\"space-y-6\">\n          {/* İstatistik Kartları */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"content-overlay p-6 hover-glow\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Users className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Toplam Hayvan</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{animals.length}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"content-overlay p-6 hover-glow\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <Wheat className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Yem Çeşidi</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{feeds.length}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"content-overlay p-6 hover-glow\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <TrendingUp className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Simülasyonlar</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{simulations.length}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"content-overlay p-6 hover-glow\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <Building2 className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Ahır Kapasitesi</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{farm.barn_capacity || 0}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Çiftlik Detayları */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Genel Bilgiler */}\n            <div className=\"content-overlay p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Genel Bilgiler</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Toplam Arazi:</span>\n                  <span className=\"font-medium\">{farm.total_land_hectares || 0} hektar</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Mera Alanı:</span>\n                  <span className=\"font-medium\">{farm.pasture_land_hectares || 0} hektar</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Ahır Kapasitesi:</span>\n                  <span className=\"font-medium\">{farm.barn_capacity || 0} hayvan</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Sağım Salonu:</span>\n                  <span className=\"font-medium\">{farm.milking_parlor_capacity || 0} hayvan</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Depolama Kapasiteleri */}\n            <div className=\"content-overlay p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Depolama Kapasiteleri</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Yem Depolama:</span>\n                  <span className=\"font-medium\">{farm.feed_storage_capacity_tons || 0} ton</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Silaj Kapasitesi:</span>\n                  <span className=\"font-medium\">{farm.silage_capacity_tons || 0} ton</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Saman Depolama:</span>\n                  <span className=\"font-medium\">{farm.hay_storage_capacity_tons || 0} ton</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Su Depolama:</span>\n                  <span className=\"font-medium\">{farm.water_storage_capacity_liters || 0} litre</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Hayvanlar Sekmesi */}\n      {activeTab === 'animals' && (\n        <div className=\"space-y-6\">\n          <div className=\"flex justify-between items-center\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Hayvanlar ({animals.length})</h3>\n            <Link\n              href={`/animals/new?farm=${farmId}`}\n              className=\"btn-primary text-white px-4 py-2 rounded-md\"\n            >\n              Yeni Hayvan Ekle\n            </Link>\n          </div>\n\n          {animals.length === 0 ? (\n            <div className=\"content-overlay p-8 text-center\">\n              <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Henüz hayvan yok</h3>\n              <p className=\"text-gray-600 mb-4\">Bu çiftlikte henüz kayıtlı hayvan bulunmuyor.</p>\n              <Link\n                href={`/animals/new?farm=${farmId}`}\n                className=\"btn-primary text-white px-6 py-3 rounded-md\"\n              >\n                İlk Hayvanı Ekle\n              </Link>\n            </div>\n          ) : (\n            <div className=\"content-overlay overflow-hidden\">\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Hayvan\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Irk\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Yaş\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Ağırlık\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Durum\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        İşlemler\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {animals.slice(0, 10).map((animal) => (\n                      <tr key={animal.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-green-100 flex items-center justify-center\">\n                                <span className=\"text-green-600 font-medium\">\n                                  {animal.tag || animal.id.slice(0, 3).toUpperCase()}\n                                </span>\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">\n                                {animal.tag || `Hayvan ${animal.id.slice(0, 8)}`}\n                              </div>\n                              <div className=\"text-sm text-gray-500\">{animal.gender === 'male' ? 'Erkek' : 'Dişi'}</div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {animal.breed}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {animal.age_months} ay\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {animal.current_weight_kg} kg\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            animal.status === 'healthy' ? 'bg-green-100 text-green-800' :\n                            animal.status === 'sick' ? 'bg-red-100 text-red-800' :\n                            'bg-yellow-100 text-yellow-800'\n                          }`}>\n                            {animal.status}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <Link\n                            href={`/animals/${animal.id}`}\n                            className=\"text-green-600 hover:text-green-900\"\n                          >\n                            Detay\n                          </Link>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n              {animals.length > 10 && (\n                <div className=\"px-6 py-3 bg-gray-50 text-center\">\n                  <Link\n                    href={`/animals?farm=${farmId}`}\n                    className=\"text-green-600 hover:text-green-900 text-sm font-medium\"\n                  >\n                    Tüm hayvanları görüntüle ({animals.length})\n                  </Link>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Yemler Sekmesi */}\n      {activeTab === 'feeds' && (\n        <div className=\"space-y-6\">\n          <div className=\"flex justify-between items-center\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Yemler ({feeds.length})</h3>\n            <Link\n              href={`/feeds/new?farm=${farmId}`}\n              className=\"btn-primary text-white px-4 py-2 rounded-md\"\n            >\n              Yeni Yem Ekle\n            </Link>\n          </div>\n\n          {feeds.length === 0 ? (\n            <div className=\"content-overlay p-8 text-center\">\n              <Wheat className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Henüz yem yok</h3>\n              <p className=\"text-gray-600 mb-4\">Bu çiftlikte henüz kayıtlı yem bulunmuyor.</p>\n              <Link\n                href={`/feeds/new?farm=${farmId}`}\n                className=\"btn-primary text-white px-6 py-3 rounded-md\"\n              >\n                İlk Yemi Ekle\n              </Link>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {feeds.map((feed) => (\n                <div key={feed.id} className=\"content-overlay p-6 hover-glow\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h4 className=\"text-lg font-medium text-gray-900\">{feed.name}</h4>\n                    <span className=\"text-sm text-gray-500\">{feed.feed_type}</span>\n                  </div>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Maliyet:</span>\n                      <span className=\"font-medium\">{feed.cost_per_kg} ₺/kg</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Protein:</span>\n                      <span className=\"font-medium\">{feed.crude_protein_percentage}%</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Enerji:</span>\n                      <span className=\"font-medium\">{feed.metabolizable_energy_mcal_kg} Mcal/kg</span>\n                    </div>\n                  </div>\n                  <div className=\"mt-4\">\n                    <Link\n                      href={`/feeds/${feed.id}`}\n                      className=\"text-green-600 hover:text-green-900 text-sm font-medium\"\n                    >\n                      Detayları Görüntüle →\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Simülasyonlar Sekmesi */}\n      {activeTab === 'simulations' && (\n        <div className=\"space-y-6\">\n          <div className=\"flex justify-between items-center\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Simülasyonlar ({simulations.length})</h3>\n            <Link\n              href={`/simulation/create?farm=${farmId}`}\n              className=\"btn-primary text-white px-4 py-2 rounded-md\"\n            >\n              Yeni Simülasyon\n            </Link>\n          </div>\n\n          {simulations.length === 0 ? (\n            <div className=\"content-overlay p-8 text-center\">\n              <TrendingUp className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Henüz simülasyon yok</h3>\n              <p className=\"text-gray-600 mb-4\">Bu çiftlik için henüz simülasyon çalıştırılmamış.</p>\n              <Link\n                href={`/simulation/create?farm=${farmId}`}\n                className=\"btn-primary text-white px-6 py-3 rounded-md\"\n              >\n                İlk Simülasyonu Çalıştır\n              </Link>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {simulations.map((simulation) => (\n                <div key={simulation.id} className=\"content-overlay p-6 hover-glow\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"text-lg font-medium text-gray-900\">{simulation.name}</h4>\n                      <p className=\"text-gray-600 mt-1\">{simulation.description}</p>\n                      <div className=\"flex items-center space-x-4 mt-2 text-sm text-gray-500\">\n                        <span>Süre: {simulation.start_age_months}-{simulation.end_age_months} ay</span>\n                        <span>Toplam Maliyet: {simulation.total_feed_cost?.toFixed(2)} ₺</span>\n                        <span>Net Kar: {simulation.net_profit?.toFixed(2)} ₺</span>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Link\n                        href={`/simulation/${simulation.id}`}\n                        className=\"text-green-600 hover:text-green-900 text-sm font-medium\"\n                      >\n                        Detayları Görüntüle\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AArBA;;;;;;AAwBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YAEX,oCAAoC;YACpC,MAAM,CAAC,UAAU,aAAa,WAAW,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5E,yHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC,IAAM;gBACpC,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,QAAQ,KAAK,CAAC,IAAM,EAAE;gBACjD,yHAAA,CAAA,UAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,CAAC,IAAM,EAAE;gBAC7C,yHAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC,QAAQ,KAAK,CAAC,IAAM,EAAE;aAC1D;YAED,IAAI,CAAC,UAAU;gBACb,SAAS;gBACT;YACF;YAEA,QAAQ;YACR,WAAW;YACX,SAAS;YACT,eAAe;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU;YACvC,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;QAEX,MAAM,YAAY,OAAO,OAAO,CAC9B,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,wEAAwE,CAAC;QAGzF,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,yHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;gBACzB,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU;YACzC;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;gBAGlD,uBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;8BAM7C,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;IAId;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM,sMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,IAAI;YAAW,MAAM;YAAa,MAAM,uMAAA,CAAA,QAAK;QAAC;QAChD;YAAE,IAAI;YAAS,MAAM;YAAU,MAAM,uMAAA,CAAA,QAAK;QAAC;QAC3C;YAAE,IAAI;YAAe,MAAM;YAAiB,MAAM,qNAAA,CAAA,aAAU;QAAC;KAC9D;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,KAAK,IAAI;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAM,KAAK,QAAQ;;;;;;;;;;;;0DAEtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;4DAAK;4DAAU,IAAI,KAAK,KAAK,gBAAgB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;gCAC7B,WAAU;;kDAEV,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,OAAO,IAAI,IAAI;4BACrB,qBACE,6LAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,uFAAuF,EACjG,cAAc,IAAI,EAAE,GAChB,oCACA,8EACJ;;kDAEF,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,IAAI,IAAI;;;;;;;+BATV,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;;;;;;YAML,cAAc,4BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKrE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKnE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,KAAK,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,mBAAmB,IAAI;4DAAE;;;;;;;;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,qBAAqB,IAAI;4DAAE;;;;;;;;;;;;;0DAEjE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,aAAa,IAAI;4DAAE;;;;;;;;;;;;;0DAEzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,uBAAuB,IAAI;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,0BAA0B,IAAI;4DAAE;;;;;;;;;;;;;0DAEtE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,oBAAoB,IAAI;4DAAE;;;;;;;;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,yBAAyB,IAAI;4DAAE;;;;;;;;;;;;;0DAErE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,6BAA6B,IAAI;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASlF,cAAc,2BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;oCAAY,QAAQ,MAAM;oCAAC;;;;;;;0CAC/E,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,kBAAkB,EAAE,QAAQ;gCACnC,WAAU;0CACX;;;;;;;;;;;;oBAKF,QAAQ,MAAM,KAAK,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,kBAAkB,EAAE,QAAQ;gCACnC,WAAU;0CACX;;;;;;;;;;;6CAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACzB,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAK,WAAU;0FACb,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;;;;;kFAItD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI;;;;;;0FAElD,6LAAC;gFAAI,WAAU;0FAAyB,OAAO,MAAM,KAAK,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;sEAInF,6LAAC;4DAAG,WAAU;sEACX,OAAO,KAAK;;;;;;sEAEf,6LAAC;4DAAG,WAAU;;gEACX,OAAO,UAAU;gEAAC;;;;;;;sEAErB,6LAAC;4DAAG,WAAU;;gEACX,OAAO,iBAAiB;gEAAC;;;;;;;sEAE5B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,OAAO,MAAM,KAAK,YAAY,gCAC9B,OAAO,MAAM,KAAK,SAAS,4BAC3B,iCACA;0EACC,OAAO,MAAM;;;;;;;;;;;sEAGlB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;gEAC7B,WAAU;0EACX;;;;;;;;;;;;mDAxCI,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;4BAiDzB,QAAQ,MAAM,GAAG,oBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,cAAc,EAAE,QAAQ;oCAC/B,WAAU;;wCACX;wCAC4B,QAAQ,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;YAUvD,cAAc,yBACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;oCAAS,MAAM,MAAM;oCAAC;;;;;;;0CAC1E,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,gBAAgB,EAAE,QAAQ;gCACjC,WAAU;0CACX;;;;;;;;;;;;oBAKF,MAAM,MAAM,KAAK,kBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,gBAAgB,EAAE,QAAQ;gCACjC,WAAU;0CACX;;;;;;;;;;;6CAKH,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqC,KAAK,IAAI;;;;;;0DAC5D,6LAAC;gDAAK,WAAU;0DAAyB,KAAK,SAAS;;;;;;;;;;;;kDAEzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,WAAW;4DAAC;;;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,wBAAwB;4DAAC;;;;;;;;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,KAAK,4BAA4B;4DAAC;;;;;;;;;;;;;;;;;;;kDAGrE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;4CACzB,WAAU;sDACX;;;;;;;;;;;;+BAvBK,KAAK,EAAE;;;;;;;;;;;;;;;;YAmC1B,cAAc,+BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;oCAAgB,YAAY,MAAM;oCAAC;;;;;;;0CACvF,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,wBAAwB,EAAE,QAAQ;gCACzC,WAAU;0CACX;;;;;;;;;;;;oBAKF,YAAY,MAAM,KAAK,kBACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,wBAAwB,EAAE,QAAQ;gCACzC,WAAU;0CACX;;;;;;;;;;;6CAKH,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;gCAAwB,WAAU;0CACjC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqC,WAAW,IAAI;;;;;;8DAClE,6LAAC;oDAAE,WAAU;8DAAsB,WAAW,WAAW;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAK;gEAAO,WAAW,gBAAgB;gEAAC;gEAAE,WAAW,cAAc;gEAAC;;;;;;;sEACrE,6LAAC;;gEAAK;gEAAiB,WAAW,eAAe,EAAE,QAAQ;gEAAG;;;;;;;sEAC9D,6LAAC;;gEAAK;gEAAU,WAAW,UAAU,EAAE,QAAQ;gEAAG;;;;;;;;;;;;;;;;;;;sDAGtD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE;gDACpC,WAAU;0DACX;;;;;;;;;;;;;;;;;+BAfG,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;AA4BvC;GA3gBwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}