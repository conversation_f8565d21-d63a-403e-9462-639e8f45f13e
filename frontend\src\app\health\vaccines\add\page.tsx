'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, Shield, Calendar, User, Syringe } from 'lucide-react';
import { farmApi, animalApi, healthApi } from '@/services/api';
import { Farm, Animal } from '@/types';

export default function AddVaccineRecordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const farmId = searchParams.get('farm');
  const animalId = searchParams.get('animal');

  const [farms, setFarms] = useState<Farm[]>([]);
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>(farmId || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    animal_id: animalId || '',
    vaccine_name: '',
    vaccine_type: 'viral',
    administered_date: new Date().toISOString().split('T')[0],
    veterinarian_name: '',
    batch_number: '',
    expiry_date: '',
    dose_amount: '',
    administration_route: 'intramuscular',
    cost: '',
    notes: '',
    next_due_date: '',
    priority: 'medium'
  });

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadAnimals();
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (!selectedFarmId && farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    }
  };

  const loadAnimals = async () => {
    try {
      const animalsData = await animalApi.getAnimalsByFarm(selectedFarmId);
      setAnimals(Array.isArray(animalsData) ? animalsData : []);
    } catch (err) {
      setError('Hayvan verileri yüklenirken hata oluştu');
      console.error('Error loading animals:', err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.animal_id || !formData.vaccine_name || !formData.administered_date) {
      setError('Hayvan, aşı adı ve uygulama tarihi zorunludur');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const vaccineData = {
        ...formData,
        cost: formData.cost ? parseFloat(formData.cost) : null,
        dose_amount: formData.dose_amount || null,
        expiry_date: formData.expiry_date || null,
        next_due_date: formData.next_due_date || null
      };

      await healthApi.recordVaccination(vaccineData);
      router.push('/health/vaccines');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Aşı kaydı oluşturulurken hata oluştu');
      console.error('Error creating vaccine record:', err);
    } finally {
      setLoading(false);
    }
  };

  const vaccineTypes = [
    { value: 'viral', label: 'Viral' },
    { value: 'bacterial', label: 'Bakteriyel' },
    { value: 'parasitic', label: 'Paraziter' },
    { value: 'combination', label: 'Kombine' },
    { value: 'other', label: 'Diğer' }
  ];

  const administrationRoutes = [
    { value: 'intramuscular', label: 'Kas İçi (IM)' },
    { value: 'subcutaneous', label: 'Deri Altı (SC)' },
    { value: 'intravenous', label: 'Damar İçi (IV)' },
    { value: 'oral', label: 'Ağızdan' },
    { value: 'nasal', label: 'Burun' },
    { value: 'other', label: 'Diğer' }
  ];

  const priorities = [
    { value: 'low', label: 'Düşük' },
    { value: 'medium', label: 'Orta' },
    { value: 'high', label: 'Yüksek' }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/health/vaccines"
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Aşı Kaydı Ekle</h1>
          <p className="text-gray-600 mt-1">
            Yeni aşı kaydı oluşturun ve hatırlatma ayarlayın
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Hayvan Seçimi */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Hayvan Bilgileri
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Çiftlik *
              </label>
              <select
                value={selectedFarmId}
                onChange={(e) => setSelectedFarmId(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              >
                <option value="">Çiftlik seçin</option>
                {farms.map((farm) => (
                  <option key={farm.id} value={farm.id}>
                    {farm.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hayvan *
              </label>
              <select
                name="animal_id"
                value={formData.animal_id}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              >
                <option value="">Hayvan seçin</option>
                {animals.map((animal) => (
                  <option key={animal.id} value={animal.id}>
                    {animal.tag || `Hayvan ${animal.id.slice(0, 8)}`} - {animal.breed}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Aşı Bilgileri */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Aşı Bilgileri
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Aşı Adı *
              </label>
              <input
                type="text"
                name="vaccine_name"
                value={formData.vaccine_name}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Örn: FMD, IBR, BVD"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Aşı Türü
              </label>
              <select
                name="vaccine_type"
                value={formData.vaccine_type}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {vaccineTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Uygulama Tarihi *
              </label>
              <input
                type="date"
                name="administered_date"
                value={formData.administered_date}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Veteriner Hekim
              </label>
              <input
                type="text"
                name="veterinarian_name"
                value={formData.veterinarian_name}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Veteriner hekim adı"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Parti Numarası
              </label>
              <input
                type="text"
                name="batch_number"
                value={formData.batch_number}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Aşı parti numarası"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Son Kullanma Tarihi
              </label>
              <input
                type="date"
                name="expiry_date"
                value={formData.expiry_date}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Uygulama Detayları */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Syringe className="h-5 w-5 mr-2" />
            Uygulama Detayları
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Doz Miktarı
              </label>
              <input
                type="text"
                name="dose_amount"
                value={formData.dose_amount}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Örn: 2 ml, 1 doz"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Uygulama Yolu
              </label>
              <select
                name="administration_route"
                value={formData.administration_route}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {administrationRoutes.map((route) => (
                  <option key={route.value} value={route.value}>
                    {route.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maliyet (₺)
              </label>
              <input
                type="number"
                name="cost"
                value={formData.cost}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="0.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notlar
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Ek notlar, yan etkiler, özel durumlar..."
              />
            </div>
          </div>
        </div>

        {/* Hatırlatma Ayarları */}
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Hatırlatma Ayarları
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sonraki Aşı Tarihi
              </label>
              <input
                type="date"
                name="next_due_date"
                value={formData.next_due_date}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                Bu tarih için otomatik hatırlatma oluşturulacak
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hatırlatma Önceliği
              </label>
              <select
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {priorities.map((priority) => (
                  <option key={priority.value} value={priority.value}>
                    {priority.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Form Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6">
          <Link
            href="/health/vaccines"
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="flex items-center space-x-2 btn-primary text-white px-6 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Kaydediliyor...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Aşı Kaydını Kaydet</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
