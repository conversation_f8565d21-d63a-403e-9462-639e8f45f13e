'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Building2,
  MapPin,
  Calendar,
  Users,
  Wheat,
  Droplets,
  Scale,
  Home,
  AlertCircle,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { farmApi, animalApi, feedApi, simulationApi } from '@/services/api';
import { Farm, Animal, Feed } from '@/types';

export default function FarmDetailPage() {
  const params = useParams();
  const router = useRouter();
  const farmId = params.id as string;

  const [farm, setFarm] = useState<Farm | null>(null);
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [feeds, setFeeds] = useState<Feed[]>([]);
  const [simulations, setSimulations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadFarmData();
  }, [farmId]);

  const loadFarmData = async () => {
    try {
      setLoading(true);

      // Paralel olarak tüm verileri yükle
      const [farmData, animalsData, feedsData, simulationsData] = await Promise.all([
        farmApi.getFarm(farmId).catch(() => null),
        animalApi.getAnimalsByFarm(farmId).catch(() => []),
        feedApi.getFeedsByFarm(farmId).catch(() => []),
        simulationApi.getSimulationsByFarm(farmId).catch(() => [])
      ]);

      if (!farmData) {
        setError('Çiftlik bulunamadı');
        return;
      }

      setFarm(farmData);
      setAnimals(animalsData);
      setFeeds(feedsData);
      setSimulations(simulationsData);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Çiftlik verileri yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFarm = async () => {
    if (!farm) return;

    const confirmed = window.confirm(
      `"${farm.name}" çiftliğini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`
    );

    if (confirmed) {
      try {
        await farmApi.deleteFarm(farmId);
        router.push('/farms');
      } catch (err: any) {
        setError(err.response?.data?.detail || 'Çiftlik silinirken hata oluştu');
      }
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Çiftlik verileri yükleniyor...</p>
      </div>
    );
  }

  if (error || !farm) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Çiftlik Bulunamadı</h1>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        <Link
          href="/farms"
          className="btn-primary text-white px-6 py-3 rounded-md inline-flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Çiftlik Listesine Dön</span>
        </Link>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Genel Bakış', icon: Home },
    { id: 'animals', name: 'Hayvanlar', icon: Users },
    { id: 'feeds', name: 'Yemler', icon: Wheat },
    { id: 'simulations', name: 'Simülasyonlar', icon: TrendingUp },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/farms"
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{farm.name}</h1>
            <div className="flex items-center space-x-4 mt-2 text-gray-600">
              <div className="flex items-center space-x-1">
                <MapPin className="h-4 w-4" />
                <span>{farm.location}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Kuruluş: {new Date(farm.established_date).toLocaleDateString('tr-TR')}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Link
            href={`/farms/${farmId}/edit`}
            className="flex items-center space-x-2 px-4 py-2 border border-green-600 text-green-600 rounded-md hover:bg-green-50 transition-colors"
          >
            <Edit className="h-4 w-4" />
            <span>Düzenle</span>
          </Link>
          <button
            onClick={handleDeleteFarm}
            className="flex items-center space-x-2 px-4 py-2 border border-red-600 text-red-600 rounded-md hover:bg-red-50 transition-colors"
          >
            <Trash2 className="h-4 w-4" />
            <span>Sil</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="content-overlay">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* İstatistik Kartları */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="content-overlay p-6 hover-glow">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Toplam Hayvan</p>
                  <p className="text-2xl font-bold text-gray-900">{animals.length}</p>
                </div>
              </div>
            </div>

            <div className="content-overlay p-6 hover-glow">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Wheat className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Yem Çeşidi</p>
                  <p className="text-2xl font-bold text-gray-900">{feeds.length}</p>
                </div>
              </div>
            </div>

            <div className="content-overlay p-6 hover-glow">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Simülasyonlar</p>
                  <p className="text-2xl font-bold text-gray-900">{simulations.length}</p>
                </div>
              </div>
            </div>

            <div className="content-overlay p-6 hover-glow">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Building2 className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Ahır Kapasitesi</p>
                  <p className="text-2xl font-bold text-gray-900">{farm.barn_capacity || 0}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Çiftlik Detayları */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Genel Bilgiler */}
            <div className="content-overlay p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Genel Bilgiler</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Toplam Arazi:</span>
                  <span className="font-medium">{farm.total_land_hectares || 0} hektar</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Mera Alanı:</span>
                  <span className="font-medium">{farm.pasture_land_hectares || 0} hektar</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Ahır Kapasitesi:</span>
                  <span className="font-medium">{farm.barn_capacity || 0} hayvan</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Sağım Salonu:</span>
                  <span className="font-medium">{farm.milking_parlor_capacity || 0} hayvan</span>
                </div>
              </div>
            </div>

            {/* Depolama Kapasiteleri */}
            <div className="content-overlay p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Depolama Kapasiteleri</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Yem Depolama:</span>
                  <span className="font-medium">{farm.feed_storage_capacity_tons || 0} ton</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Silaj Kapasitesi:</span>
                  <span className="font-medium">{farm.silage_capacity_tons || 0} ton</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Saman Depolama:</span>
                  <span className="font-medium">{farm.hay_storage_capacity_tons || 0} ton</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Su Depolama:</span>
                  <span className="font-medium">{farm.water_storage_capacity_liters || 0} litre</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hayvanlar Sekmesi */}
      {activeTab === 'animals' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">Hayvanlar ({animals.length})</h3>
            <Link
              href={`/animals/new?farm=${farmId}`}
              className="btn-primary text-white px-4 py-2 rounded-md"
            >
              Yeni Hayvan Ekle
            </Link>
          </div>

          {animals.length === 0 ? (
            <div className="content-overlay p-8 text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz hayvan yok</h3>
              <p className="text-gray-600 mb-4">Bu çiftlikte henüz kayıtlı hayvan bulunmuyor.</p>
              <Link
                href={`/animals/new?farm=${farmId}`}
                className="btn-primary text-white px-6 py-3 rounded-md"
              >
                İlk Hayvanı Ekle
              </Link>
            </div>
          ) : (
            <div className="content-overlay overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hayvan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Irk
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Yaş
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ağırlık
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Durum
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        İşlemler
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {animals.slice(0, 10).map((animal) => (
                      <tr key={animal.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                <span className="text-green-600 font-medium">
                                  {animal.tag || animal.id.slice(0, 3).toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {animal.tag || `Hayvan ${animal.id.slice(0, 8)}`}
                              </div>
                              <div className="text-sm text-gray-500">{animal.gender === 'male' ? 'Erkek' : 'Dişi'}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {animal.breed}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {animal.age_months} ay
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {animal.current_weight_kg} kg
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            animal.status === 'healthy' ? 'bg-green-100 text-green-800' :
                            animal.status === 'sick' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {animal.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/animals/${animal.id}`}
                            className="text-green-600 hover:text-green-900"
                          >
                            Detay
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {animals.length > 10 && (
                <div className="px-6 py-3 bg-gray-50 text-center">
                  <Link
                    href={`/animals?farm=${farmId}`}
                    className="text-green-600 hover:text-green-900 text-sm font-medium"
                  >
                    Tüm hayvanları görüntüle ({animals.length})
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Yemler Sekmesi */}
      {activeTab === 'feeds' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">Yemler ({feeds.length})</h3>
            <Link
              href={`/feeds/new?farm=${farmId}`}
              className="btn-primary text-white px-4 py-2 rounded-md"
            >
              Yeni Yem Ekle
            </Link>
          </div>

          {feeds.length === 0 ? (
            <div className="content-overlay p-8 text-center">
              <Wheat className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz yem yok</h3>
              <p className="text-gray-600 mb-4">Bu çiftlikte henüz kayıtlı yem bulunmuyor.</p>
              <Link
                href={`/feeds/new?farm=${farmId}`}
                className="btn-primary text-white px-6 py-3 rounded-md"
              >
                İlk Yemi Ekle
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {feeds.map((feed) => (
                <div key={feed.id} className="content-overlay p-6 hover-glow">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-medium text-gray-900">{feed.name}</h4>
                    <span className="text-sm text-gray-500">{feed.feed_type}</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Maliyet:</span>
                      <span className="font-medium">{feed.cost_per_kg} ₺/kg</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Protein:</span>
                      <span className="font-medium">{feed.crude_protein_percentage}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Enerji:</span>
                      <span className="font-medium">{feed.metabolizable_energy_mcal_kg} Mcal/kg</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Link
                      href={`/feeds/${feed.id}`}
                      className="text-green-600 hover:text-green-900 text-sm font-medium"
                    >
                      Detayları Görüntüle →
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Simülasyonlar Sekmesi */}
      {activeTab === 'simulations' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">Simülasyonlar ({simulations.length})</h3>
            <Link
              href={`/simulation/create?farm=${farmId}`}
              className="btn-primary text-white px-4 py-2 rounded-md"
            >
              Yeni Simülasyon
            </Link>
          </div>

          {simulations.length === 0 ? (
            <div className="content-overlay p-8 text-center">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz simülasyon yok</h3>
              <p className="text-gray-600 mb-4">Bu çiftlik için henüz simülasyon çalıştırılmamış.</p>
              <Link
                href={`/simulation/create?farm=${farmId}`}
                className="btn-primary text-white px-6 py-3 rounded-md"
              >
                İlk Simülasyonu Çalıştır
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {simulations.map((simulation) => (
                <div key={simulation.id} className="content-overlay p-6 hover-glow">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{simulation.name}</h4>
                      <p className="text-gray-600 mt-1">{simulation.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>Süre: {simulation.start_age_months}-{simulation.end_age_months} ay</span>
                        <span>Toplam Maliyet: {simulation.total_feed_cost?.toFixed(2)} ₺</span>
                        <span>Net Kar: {simulation.net_profit?.toFixed(2)} ₺</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Link
                        href={`/simulation/${simulation.id}`}
                        className="text-green-600 hover:text-green-900 text-sm font-medium"
                      >
                        Detayları Görüntüle
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
