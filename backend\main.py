from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime, timedelta
import uuid
import sqlite3
import json
from typing import List, Dict, Any

# SQLite veritabanı kurulumu
def init_database():
    conn = sqlite3.connect('hayvancilik.db')
    cursor = conn.cursor()

    # Çiftlik tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS farms (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            location TEXT NOT NULL,
            established_date TEXT NOT NULL,
            total_land_hectares REAL,
            pasture_land_hectares REAL,
            barn_capacity INTEGER,
            feed_storage_capacity_tons REAL,
            silage_capacity_tons REAL,
            hay_storage_capacity_tons REAL,
            water_storage_capacity_liters REAL,
            milking_parlor_capacity INTEGER,
            quarantine_facility_capacity INTEGER,
            hospital_pen_capacity INTEGER,
            handling_facility_present BOOLEAN,
            scale_capacity_kg REAL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Hayvan tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS animals (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            tag TEXT NOT NULL,
            breed TEXT NOT NULL,
            birth_date TEXT NOT NULL,
            gender TEXT NOT NULL,
            current_weight_kg REAL NOT NULL,
            body_condition_score REAL NOT NULL,
            status TEXT NOT NULL,
            is_pregnant BOOLEAN DEFAULT FALSE,
            pregnancy_start_date TEXT,
            expected_calving_date TEXT,
            dam_id TEXT,
            sire_id TEXT,
            purchase_price REAL,
            purchase_date TEXT,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id)
        )
    ''')

    # Yem tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS feeds (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            name TEXT NOT NULL,
            feed_type TEXT NOT NULL,
            cost_per_kg REAL NOT NULL,
            dry_matter_percentage REAL NOT NULL,
            crude_protein_percentage REAL NOT NULL,
            metabolizable_energy_mcal_kg REAL NOT NULL,
            storage_life_days INTEGER,
            moisture_content_percentage REAL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id)
        )
    ''')

    # Rasyon tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS rations (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            animal_id TEXT,
            name TEXT NOT NULL,
            description TEXT,
            ration_type TEXT NOT NULL DEFAULT 'individual',
            target_group TEXT,
            total_cost_per_day REAL NOT NULL,
            total_dry_matter_kg REAL NOT NULL,
            total_crude_protein_percentage REAL NOT NULL,
            total_metabolizable_energy_mcal REAL NOT NULL,
            total_calcium_percentage REAL NOT NULL,
            total_phosphorus_percentage REAL NOT NULL,
            is_optimized BOOLEAN DEFAULT FALSE,
            optimization_objective TEXT,
            optimization_score REAL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id),
            FOREIGN KEY (animal_id) REFERENCES animals (id)
        )
    ''')

    # Rasyon bileşenleri tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ration_components (
            id TEXT PRIMARY KEY,
            ration_id TEXT NOT NULL,
            feed_id TEXT NOT NULL,
            amount_kg_per_day REAL NOT NULL,
            percentage_of_total_dm REAL NOT NULL,
            dry_matter_contribution_kg REAL NOT NULL,
            protein_contribution_kg REAL NOT NULL,
            energy_contribution_mcal REAL NOT NULL,
            calcium_contribution_kg REAL NOT NULL,
            phosphorus_contribution_kg REAL NOT NULL,
            cost_contribution REAL NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ration_id) REFERENCES rations (id),
            FOREIGN KEY (feed_id) REFERENCES feeds (id)
        )
    ''')

    # Simülasyon tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS simulations (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            animal_id TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            simulation_type TEXT NOT NULL DEFAULT 'lifetime',
            start_age_months INTEGER NOT NULL,
            end_age_months INTEGER NOT NULL,
            target_weight_kg REAL,
            slaughter_age_months INTEGER,
            breeding_start_months INTEGER,
            total_feed_cost REAL NOT NULL,
            total_feed_consumption_kg REAL NOT NULL,
            total_revenue REAL,
            net_profit REAL,
            roi_percentage REAL,
            break_even_months INTEGER,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id),
            FOREIGN KEY (animal_id) REFERENCES animals (id)
        )
    ''')

    # Simülasyon detayları (aylık)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS simulation_details (
            id TEXT PRIMARY KEY,
            simulation_id TEXT NOT NULL,
            month INTEGER NOT NULL,
            age_months INTEGER NOT NULL,
            weight_kg REAL NOT NULL,
            daily_gain_kg REAL NOT NULL,
            feed_consumption_kg_per_day REAL NOT NULL,
            monthly_feed_cost REAL NOT NULL,
            monthly_feed_consumption_kg REAL NOT NULL,
            physiological_stage TEXT NOT NULL,
            is_pregnant BOOLEAN DEFAULT FALSE,
            is_lactating BOOLEAN DEFAULT FALSE,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (simulation_id) REFERENCES simulations (id)
        )
    ''')

    # Irk özellikleri tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS breed_characteristics (
            id TEXT PRIMARY KEY,
            breed TEXT NOT NULL UNIQUE,
            mature_weight_male_kg REAL NOT NULL,
            mature_weight_female_kg REAL NOT NULL,
            birth_weight_kg REAL NOT NULL,
            weaning_weight_kg REAL NOT NULL,
            yearling_weight_kg REAL NOT NULL,
            average_daily_gain_kg REAL NOT NULL,
            feed_conversion_ratio REAL NOT NULL,
            dressing_percentage REAL NOT NULL,
            age_at_first_calving_months INTEGER NOT NULL,
            gestation_length_days INTEGER NOT NULL,
            calving_interval_days INTEGER NOT NULL,
            longevity_years INTEGER NOT NULL,
            calving_ease_score REAL NOT NULL,
            milk_production_potential_kg REAL NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Aşı şemaları tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vaccine_schedules (
            id TEXT PRIMARY KEY,
            breed TEXT NOT NULL,
            vaccine_name TEXT NOT NULL,
            age_months_min INTEGER NOT NULL,
            age_months_max INTEGER,
            repeat_interval_months INTEGER,
            season_requirement TEXT,
            is_mandatory BOOLEAN DEFAULT TRUE,
            description TEXT,
            cost_estimate REAL DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Hayvan aşı kayıtları tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS animal_vaccinations (
            id TEXT PRIMARY KEY,
            animal_id TEXT NOT NULL,
            vaccine_name TEXT NOT NULL,
            vaccination_date TEXT NOT NULL,
            next_due_date TEXT,
            veterinarian TEXT,
            batch_number TEXT,
            cost REAL DEFAULT 0,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (animal_id) REFERENCES animals (id)
        )
    ''')

    # Sağlık kayıtları tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS health_records (
            id TEXT PRIMARY KEY,
            animal_id TEXT NOT NULL,
            record_type TEXT NOT NULL,
            diagnosis TEXT,
            treatment TEXT,
            medication TEXT,
            veterinarian TEXT,
            cost REAL DEFAULT 0,
            start_date TEXT NOT NULL,
            end_date TEXT,
            recovery_status TEXT DEFAULT 'ongoing',
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (animal_id) REFERENCES animals (id)
        )
    ''')

    # Çiftleşme kayıtları tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS breeding_records (
            id TEXT PRIMARY KEY,
            female_id TEXT NOT NULL,
            male_id TEXT,
            breeding_date TEXT NOT NULL,
            breeding_type TEXT NOT NULL,
            semen_source TEXT,
            expected_calving_date TEXT,
            pregnancy_confirmed BOOLEAN DEFAULT FALSE,
            pregnancy_check_date TEXT,
            pregnancy_check_method TEXT,
            veterinarian TEXT,
            cost REAL DEFAULT 0,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (female_id) REFERENCES animals (id),
            FOREIGN KEY (male_id) REFERENCES animals (id)
        )
    ''')

    # Doğum kayıtları tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS calving_records (
            id TEXT PRIMARY KEY,
            mother_id TEXT NOT NULL,
            calf_id TEXT,
            calving_date TEXT NOT NULL,
            calving_ease INTEGER DEFAULT 1,
            birth_weight REAL,
            complications TEXT,
            veterinarian_assisted BOOLEAN DEFAULT FALSE,
            veterinarian TEXT,
            cost REAL DEFAULT 0,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (mother_id) REFERENCES animals (id),
            FOREIGN KEY (calf_id) REFERENCES animals (id)
        )
    ''')

    # Hatırlatmalar tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS reminders (
            id TEXT PRIMARY KEY,
            animal_id TEXT NOT NULL,
            reminder_type TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            due_date TEXT NOT NULL,
            priority TEXT DEFAULT 'medium',
            is_completed BOOLEAN DEFAULT FALSE,
            completed_date TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (animal_id) REFERENCES animals (id)
        )
    ''')

    # Örnek ırk verilerini ekle
    cursor.execute("SELECT COUNT(*) FROM breed_characteristics")
    if cursor.fetchone()[0] == 0:
        breed_data = [
            ('angus', 850, 550, 35, 250, 400, 1.2, 6.5, 62, 24, 283, 365, 12, 1.5, 0),
            ('holstein', 900, 650, 40, 280, 450, 1.0, 7.0, 58, 24, 283, 365, 10, 2.0, 8000),
            ('simmental', 1100, 750, 45, 320, 500, 1.3, 6.2, 65, 26, 285, 370, 14, 1.8, 6000),
            ('charolais', 1200, 800, 50, 350, 550, 1.4, 6.0, 68, 28, 287, 375, 15, 2.2, 0),
            ('limousin', 1000, 650, 42, 300, 480, 1.25, 6.3, 64, 25, 284, 368, 13, 1.6, 0),
            ('hereford', 900, 600, 38, 270, 420, 1.1, 6.8, 60, 24, 283, 365, 12, 1.4, 0)
        ]

        for breed, mature_male, mature_female, birth, weaning, yearling, adg, fcr, dress, calving_age, gestation, interval, longevity, ease, milk in breed_data:
            breed_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO breed_characteristics (
                    id, breed, mature_weight_male_kg, mature_weight_female_kg, birth_weight_kg,
                    weaning_weight_kg, yearling_weight_kg, average_daily_gain_kg, feed_conversion_ratio,
                    dressing_percentage, age_at_first_calving_months, gestation_length_days,
                    calving_interval_days, longevity_years, calving_ease_score, milk_production_potential_kg
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (breed_id, breed, mature_male, mature_female, birth, weaning, yearling, adg, fcr, dress, calving_age, gestation, interval, longevity, ease, milk))

    # Örnek aşı şemalarını ekle
    cursor.execute("SELECT COUNT(*) FROM vaccine_schedules")
    if cursor.fetchone()[0] == 0:
        vaccine_data = [
            # Genel aşılar (tüm ırklar için)
            ('all', 'Şap Hastalığı', 2, 3, 6, None, True, 'Şap hastalığına karşı koruma', 25.0),
            ('all', 'Şap Hastalığı Rapel', 8, 9, 6, None, True, 'Şap aşısı takviye', 25.0),
            ('all', 'Brucella', 3, 8, None, None, True, 'Brucella hastalığına karşı koruma (sadece dişiler)', 30.0),
            ('all', 'IBR/BVD', 2, 3, 12, None, True, 'Solunum yolu hastalıkları', 35.0),
            ('all', 'Clostridial', 2, 3, 12, None, True, 'Clostridial hastalıklar', 20.0),
            ('all', 'Anthrax', 6, 12, 12, 'spring', True, 'Şarbon hastalığı', 15.0),

            # Holstein özel aşılar
            ('holstein', 'Mastitis', 12, 15, 6, None, False, 'Meme iltihabı önleme', 40.0),
            ('holstein', 'E.coli', 1, 2, None, None, True, 'E.coli enfeksiyonu', 25.0),

            # Et ırkları özel aşılar
            ('angus', 'Respiratory Complex', 1, 2, 12, None, True, 'Solunum kompleksi', 30.0),
            ('charolais', 'Respiratory Complex', 1, 2, 12, None, True, 'Solunum kompleksi', 30.0),
            ('simmental', 'Respiratory Complex', 1, 2, 12, None, True, 'Solunum kompleksi', 30.0),
            ('limousin', 'Respiratory Complex', 1, 2, 12, None, True, 'Solunum kompleksi', 30.0),
            ('hereford', 'Respiratory Complex', 1, 2, 12, None, True, 'Solunum kompleksi', 30.0),
        ]

        for breed, vaccine, min_age, max_age, interval, season, mandatory, desc, cost in vaccine_data:
            vaccine_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO vaccine_schedules (
                    id, breed, vaccine_name, age_months_min, age_months_max,
                    repeat_interval_months, season_requirement, is_mandatory, description, cost_estimate
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (vaccine_id, breed, vaccine, min_age, max_age, interval, season, mandatory, desc, cost))

    conn.commit()
    conn.close()
    print("✓ Veritabanı başarıyla oluşturuldu")

def get_db_connection():
    conn = sqlite3.connect('hayvancilik.db')
    conn.row_factory = sqlite3.Row  # Dict-like access
    return conn

# Veritabanını başlat
init_database()

# FastAPI uygulaması
app = FastAPI(
    title="Hayvan Yetiştiriciliği Simülasyon Sistemi",
    description="Sığır yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi",
    version="1.0.0"
)

# CORS middleware ekle
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Hayvan Yetiştiriciliği Simülasyon Sistemi API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Çiftlik API endpoint'leri
@app.get("/api/farms/")
async def get_farms():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM farms ORDER BY created_at DESC")
    farms = cursor.fetchall()
    conn.close()

    return [dict(farm) for farm in farms]

@app.post("/api/farms/")
async def create_farm(farm_data: dict):
    try:
        farm_id = str(uuid.uuid4())
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO farms (
                id, name, location, established_date,
                total_land_hectares, pasture_land_hectares, barn_capacity,
                feed_storage_capacity_tons, silage_capacity_tons, hay_storage_capacity_tons,
                water_storage_capacity_liters, milking_parlor_capacity,
                quarantine_facility_capacity, hospital_pen_capacity,
                handling_facility_present, scale_capacity_kg
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            farm_id,
            farm_data.get('name'),
            farm_data.get('location'),
            datetime.now().isoformat(),
            farm_data.get('total_land_hectares', 0),
            farm_data.get('pasture_land_hectares', 0),
            farm_data.get('barn_capacity', 0),
            farm_data.get('feed_storage_capacity_tons', 0),
            farm_data.get('silage_capacity_tons', 0),
            farm_data.get('hay_storage_capacity_tons', 0),
            farm_data.get('water_storage_capacity_liters', 0),
            farm_data.get('milking_parlor_capacity'),
            farm_data.get('quarantine_facility_capacity', 0),
            farm_data.get('hospital_pen_capacity', 0),
            farm_data.get('handling_facility_present', False),
            farm_data.get('scale_capacity_kg', 0)
        ))

        conn.commit()
        conn.close()

        print(f"✓ Çiftlik oluşturuldu: {farm_data.get('name')} (ID: {farm_id})")
        return {"id": farm_id, "message": "Çiftlik başarıyla oluşturuldu"}

    except Exception as e:
        print(f"❌ Çiftlik oluşturma hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Çiftlik oluşturulamadı: {str(e)}")

@app.get("/api/farms/{farm_id}")
async def get_farm(farm_id: str):
    """Belirli bir çiftliği getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM farms WHERE id = ?", (farm_id,))
        farm = cursor.fetchone()
        conn.close()

        if not farm:
            raise HTTPException(status_code=404, detail="Çiftlik bulunamadı")

        return dict(farm)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Çiftlik getirme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Çiftlik getirilemedi: {str(e)}")

@app.put("/api/farms/{farm_id}")
async def update_farm(farm_id: str, farm_data: dict):
    """Çiftlik güncelle"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce çiftliğin var olup olmadığını kontrol et
        cursor.execute("SELECT id FROM farms WHERE id = ?", (farm_id,))
        if not cursor.fetchone():
            conn.close()
            raise HTTPException(status_code=404, detail="Çiftlik bulunamadı")

        cursor.execute('''
            UPDATE farms SET
                name = ?,
                location = ?,
                total_land_hectares = ?,
                pasture_land_hectares = ?,
                barn_capacity = ?,
                feed_storage_capacity_tons = ?,
                silage_capacity_tons = ?,
                hay_storage_capacity_tons = ?,
                water_storage_capacity_liters = ?,
                milking_parlor_capacity = ?,
                quarantine_facility_capacity = ?,
                hospital_pen_capacity = ?,
                handling_facility_present = ?,
                scale_capacity_kg = ?
            WHERE id = ?
        ''', (
            farm_data.get('name'),
            farm_data.get('location'),
            farm_data.get('total_land_hectares', 0),
            farm_data.get('pasture_land_hectares', 0),
            farm_data.get('barn_capacity', 0),
            farm_data.get('feed_storage_capacity_tons', 0),
            farm_data.get('silage_capacity_tons', 0),
            farm_data.get('hay_storage_capacity_tons', 0),
            farm_data.get('water_storage_capacity_liters', 0),
            farm_data.get('milking_parlor_capacity', 0),
            farm_data.get('quarantine_facility_capacity', 0),
            farm_data.get('hospital_pen_capacity', 0),
            farm_data.get('handling_facility_present', False),
            farm_data.get('scale_capacity_kg', 0),
            farm_id
        ))

        conn.commit()
        conn.close()

        print(f"✓ Çiftlik güncellendi: {farm_data.get('name')} (ID: {farm_id})")
        return {"message": "Çiftlik başarıyla güncellendi"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Çiftlik güncelleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Çiftlik güncellenemedi: {str(e)}")

@app.delete("/api/farms/{farm_id}")
async def delete_farm(farm_id: str):
    """Çiftlik sil"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce çiftliğin var olup olmadığını kontrol et
        cursor.execute("SELECT name FROM farms WHERE id = ?", (farm_id,))
        farm = cursor.fetchone()
        if not farm:
            conn.close()
            raise HTTPException(status_code=404, detail="Çiftlik bulunamadı")

        farm_name = farm['name']

        # Çiftliğe bağlı hayvanları kontrol et
        cursor.execute("SELECT COUNT(*) as count FROM animals WHERE farm_id = ?", (farm_id,))
        animal_count = cursor.fetchone()['count']

        if animal_count > 0:
            conn.close()
            raise HTTPException(
                status_code=400,
                detail=f"Bu çiftlikte {animal_count} hayvan bulunuyor. Önce hayvanları silin veya başka çiftliğe taşıyın."
            )

        # Çiftliği sil
        cursor.execute("DELETE FROM farms WHERE id = ?", (farm_id,))
        conn.commit()
        conn.close()

        print(f"✓ Çiftlik silindi: {farm_name} (ID: {farm_id})")
        return {"message": "Çiftlik başarıyla silindi"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Çiftlik silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Çiftlik silinemedi: {str(e)}")

# Hayvan API endpoint'leri
@app.get("/api/animals/farm/{farm_id}")
async def get_animals(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM animals WHERE farm_id = ? ORDER BY created_at DESC", (farm_id,))
    animals = cursor.fetchall()
    conn.close()

    # Yaş hesaplama
    result = []
    for animal in animals:
        animal_dict = dict(animal)
        birth_date = datetime.fromisoformat(animal_dict['birth_date'])
        age_months = int((datetime.now() - birth_date).days / 30.44)
        animal_dict['age_months'] = age_months
        result.append(animal_dict)

    return result

@app.get("/api/animals/farm/{farm_id}/stats")
async def get_animal_stats(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM animals WHERE farm_id = ?", (farm_id,))
    animals = cursor.fetchall()
    conn.close()

    if not animals:
        return {
            "total_animals": 0,
            "by_gender": {"male": 0, "female": 0},
            "by_status": {},
            "by_breed": {},
            "average_weight": 0,
            "pregnant_count": 0
        }

    stats = {
        "total_animals": len(animals),
        "by_gender": {"male": 0, "female": 0},
        "by_status": {},
        "by_breed": {},
        "average_weight": sum(animal['current_weight_kg'] for animal in animals) / len(animals),
        "pregnant_count": sum(1 for animal in animals if animal['is_pregnant'])
    }

    for animal in animals:
        # Cinsiyet istatistikleri
        stats["by_gender"][animal['gender']] += 1

        # Durum istatistikleri
        if animal['status'] not in stats["by_status"]:
            stats["by_status"][animal['status']] = 0
        stats["by_status"][animal['status']] += 1

        # Irk istatistikleri
        if animal['breed'] not in stats["by_breed"]:
            stats["by_breed"][animal['breed']] = 0
        stats["by_breed"][animal['breed']] += 1

    return stats

@app.post("/api/animals/")
async def create_animal(animal_data: dict):
    try:
        animal_id = str(uuid.uuid4())
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO animals (
                id, farm_id, tag, breed, birth_date, gender,
                current_weight_kg, body_condition_score, status,
                is_pregnant, pregnancy_start_date, expected_calving_date,
                dam_id, sire_id, purchase_price, purchase_date, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            animal_id,
            animal_data.get('farm_id'),
            animal_data.get('tag', f'#{animal_id[:8]}'),
            animal_data.get('breed'),
            animal_data.get('birth_date'),
            animal_data.get('gender'),
            animal_data.get('current_weight_kg'),
            animal_data.get('body_condition_score'),
            animal_data.get('status'),
            animal_data.get('is_pregnant', False),
            animal_data.get('pregnancy_start_date'),
            animal_data.get('expected_calving_date'),
            animal_data.get('dam_id'),
            animal_data.get('sire_id'),
            animal_data.get('purchase_price'),
            animal_data.get('purchase_date'),
            animal_data.get('notes', '')
        ))

        conn.commit()
        conn.close()

        print(f"✓ Hayvan eklendi: {animal_data.get('breed')} (ID: {animal_id})")
        return {"id": animal_id, "message": "Hayvan başarıyla eklendi"}

    except Exception as e:
        print(f"❌ Hayvan ekleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hayvan eklenemedi: {str(e)}")

@app.get("/api/animals/{animal_id}")
async def get_animal(animal_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
    animal = cursor.fetchone()
    conn.close()

    if not animal:
        raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

    animal_dict = dict(animal)
    birth_date = datetime.fromisoformat(animal_dict['birth_date'])
    age_months = int((datetime.now() - birth_date).days / 30.44)
    animal_dict['age_months'] = age_months

    return animal_dict

@app.put("/api/animals/{animal_id}")
async def update_animal(animal_id: str, animal_data: dict):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce hayvanın var olduğunu kontrol et
        cursor.execute("SELECT id FROM animals WHERE id = ?", (animal_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        cursor.execute('''
            UPDATE animals SET
                breed = ?, birth_date = ?, gender = ?,
                current_weight_kg = ?, body_condition_score = ?, status = ?,
                is_pregnant = ?, pregnancy_start_date = ?, expected_calving_date = ?,
                dam_id = ?, sire_id = ?, purchase_price = ?, purchase_date = ?
            WHERE id = ?
        ''', (
            animal_data.get('breed'),
            animal_data.get('birth_date'),
            animal_data.get('gender'),
            animal_data.get('current_weight_kg'),
            animal_data.get('body_condition_score'),
            animal_data.get('status'),
            animal_data.get('is_pregnant', False),
            animal_data.get('pregnancy_start_date'),
            animal_data.get('expected_calving_date'),
            animal_data.get('dam_id'),
            animal_data.get('sire_id'),
            animal_data.get('purchase_price'),
            animal_data.get('purchase_date'),
            animal_id
        ))

        conn.commit()
        conn.close()

        print(f"✓ Hayvan güncellendi: {animal_data.get('breed')} (ID: {animal_id})")
        return {"message": "Hayvan başarıyla güncellendi"}

    except Exception as e:
        print(f"❌ Hayvan güncelleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hayvan güncellenemedi: {str(e)}")

@app.delete("/api/animals/{animal_id}")
async def delete_animal(animal_id: str):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce hayvanın var olduğunu kontrol et
        cursor.execute("SELECT id FROM animals WHERE id = ?", (animal_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        cursor.execute("DELETE FROM animals WHERE id = ?", (animal_id,))
        conn.commit()
        conn.close()

        print(f"✓ Hayvan silindi: {animal_id}")
        return {"message": "Hayvan başarıyla silindi"}

    except Exception as e:
        print(f"❌ Hayvan silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hayvan silinemedi: {str(e)}")

# Yem API endpoint'leri
@app.get("/api/feeds/farm/{farm_id}")
async def get_feeds(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM feeds WHERE farm_id = ? ORDER BY created_at DESC", (farm_id,))
    feeds = cursor.fetchall()
    conn.close()

    return [dict(feed) for feed in feeds]

@app.post("/api/feeds/")
async def create_feed(feed_data: dict):
    try:
        feed_id = str(uuid.uuid4())
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO feeds (
                id, farm_id, name, feed_type, cost_per_kg,
                dry_matter_percentage, crude_protein_percentage,
                metabolizable_energy_mcal_kg, storage_life_days,
                moisture_content_percentage
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            feed_id,
            feed_data.get('farm_id'),
            feed_data.get('name'),
            feed_data.get('feed_type'),
            feed_data.get('cost_per_kg'),
            feed_data.get('dry_matter_percentage'),
            feed_data.get('crude_protein_percentage'),
            feed_data.get('metabolizable_energy_mcal_kg'),
            feed_data.get('storage_life_days'),
            feed_data.get('moisture_content_percentage')
        ))

        conn.commit()
        conn.close()

        print(f"✓ Yem eklendi: {feed_data.get('name')} (ID: {feed_id})")
        return {"id": feed_id, "message": "Yem başarıyla eklendi"}

    except Exception as e:
        print(f"❌ Yem ekleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Yem eklenemedi: {str(e)}")

@app.post("/api/feeds/farm/{farm_id}/sample-feeds")
async def add_sample_feeds(farm_id: str):
    try:
        sample_feeds = [
            {
                "name": "Konsantre Yem",
                "feed_type": "concentrate",
                "cost_per_kg": 3.5,
                "dry_matter_percentage": 88.0,
                "crude_protein_percentage": 18.0,
                "metabolizable_energy_mcal_kg": 2.8,
                "storage_life_days": 180,
                "moisture_content_percentage": 12.0
            },
            {
                "name": "Kuru Ot",
                "feed_type": "hay",
                "cost_per_kg": 0.8,
                "dry_matter_percentage": 85.0,
                "crude_protein_percentage": 8.0,
                "metabolizable_energy_mcal_kg": 2.0,
                "storage_life_days": 365,
                "moisture_content_percentage": 15.0
            },
            {
                "name": "Mısır Silajı",
                "feed_type": "silage",
                "cost_per_kg": 0.3,
                "dry_matter_percentage": 35.0,
                "crude_protein_percentage": 7.5,
                "metabolizable_energy_mcal_kg": 2.4,
                "storage_life_days": 365,
                "moisture_content_percentage": 65.0
            }
        ]

        conn = get_db_connection()
        cursor = conn.cursor()
        added_feeds = []

        for feed_data in sample_feeds:
            feed_id = str(uuid.uuid4())
            feed_data["farm_id"] = farm_id

            cursor.execute('''
                INSERT INTO feeds (
                    id, farm_id, name, feed_type, cost_per_kg,
                    dry_matter_percentage, crude_protein_percentage,
                    metabolizable_energy_mcal_kg, storage_life_days,
                    moisture_content_percentage
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                feed_id,
                feed_data["farm_id"],
                feed_data["name"],
                feed_data["feed_type"],
                feed_data["cost_per_kg"],
                feed_data["dry_matter_percentage"],
                feed_data["crude_protein_percentage"],
                feed_data["metabolizable_energy_mcal_kg"],
                feed_data["storage_life_days"],
                feed_data["moisture_content_percentage"]
            ))
            added_feeds.append(feed_data["name"])

        conn.commit()
        conn.close()

        print(f"✓ {len(added_feeds)} örnek yem eklendi")
        return {
            "message": f"{len(added_feeds)} örnek yem başarıyla eklendi",
            "feeds": added_feeds
        }

    except Exception as e:
        print(f"❌ Örnek yem ekleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Örnek yemler eklenemedi: {str(e)}")

@app.get("/api/feeds/{feed_id}")
async def get_feed(feed_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM feeds WHERE id = ?", (feed_id,))
    feed = cursor.fetchone()
    conn.close()

    if not feed:
        raise HTTPException(status_code=404, detail="Yem bulunamadı")

    return dict(feed)

@app.put("/api/feeds/{feed_id}")
async def update_feed(feed_id: str, feed_data: dict):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce yemin var olduğunu kontrol et
        cursor.execute("SELECT id FROM feeds WHERE id = ?", (feed_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Yem bulunamadı")

        cursor.execute('''
            UPDATE feeds SET
                name = ?, feed_type = ?, cost_per_kg = ?,
                dry_matter_percentage = ?, crude_protein_percentage = ?,
                metabolizable_energy_mcal_kg = ?, storage_life_days = ?,
                moisture_content_percentage = ?
            WHERE id = ?
        ''', (
            feed_data.get('name'),
            feed_data.get('feed_type'),
            feed_data.get('cost_per_kg'),
            feed_data.get('dry_matter_percentage'),
            feed_data.get('crude_protein_percentage'),
            feed_data.get('metabolizable_energy_mcal_kg'),
            feed_data.get('storage_life_days'),
            feed_data.get('moisture_content_percentage'),
            feed_id
        ))

        conn.commit()
        conn.close()

        print(f"✓ Yem güncellendi: {feed_data.get('name')} (ID: {feed_id})")
        return {"message": "Yem başarıyla güncellendi"}

    except Exception as e:
        print(f"❌ Yem güncelleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Yem güncellenemedi: {str(e)}")

@app.delete("/api/feeds/{feed_id}")
async def delete_feed(feed_id: str):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce yemin var olduğunu kontrol et
        cursor.execute("SELECT id FROM feeds WHERE id = ?", (feed_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Yem bulunamadı")

        cursor.execute("DELETE FROM feeds WHERE id = ?", (feed_id,))
        conn.commit()
        conn.close()

        print(f"✓ Yem silindi: {feed_id}")
        return {"message": "Yem başarıyla silindi"}

    except Exception as e:
        print(f"❌ Yem silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Yem silinemedi: {str(e)}")

# Rasyon API endpoint'leri
@app.get("/api/rations/farm/{farm_id}")
async def get_rations(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM rations WHERE farm_id = ? AND is_active = 1 ORDER BY created_at DESC", (farm_id,))
    rations = cursor.fetchall()
    conn.close()

    return [dict(ration) for ration in rations]

@app.post("/api/rations/calculate-requirements")
async def calculate_requirements(animal_id: str):
    """Basit besin ihtiyacı hesaplama"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
        animal = cursor.fetchone()
        conn.close()

        if not animal:
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        animal_dict = dict(animal)
        weight = animal_dict['current_weight_kg']

        # Basit NRC hesaplamaları
        dmi = weight * 0.025  # %2.5 vücut ağırlığı
        energy = weight * 0.077 * (weight ** -0.25) / 0.64  # Basit ME hesabı
        protein = weight * 0.004  # Basit protein hesabı
        calcium = weight * 0.00004  # 40mg/kg
        phosphorus = weight * 0.00003  # 30mg/kg

        return {
            "animal_id": animal_id,
            "dry_matter_intake_kg": round(dmi, 2),
            "energy_requirements": {
                "metabolizable_energy": round(energy, 2)
            },
            "protein_requirements": {
                "crude_protein": round(protein, 3)
            },
            "mineral_requirements": {
                "calcium": round(calcium, 4),
                "phosphorus": round(phosphorus, 4)
            }
        }

    except Exception as e:
        print(f"❌ Besin ihtiyacı hesaplama hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hesaplama hatası: {str(e)}")

@app.post("/api/rations/optimize")
async def optimize_ration(request_data: dict):
    """Basit rasyon optimizasyonu"""
    try:
        farm_id = request_data.get('farm_id')
        animal_id = request_data.get('animal_id')
        name = request_data.get('name')

        # Hayvan bilgilerini al
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
        animal = cursor.fetchone()

        if not animal:
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        # Çiftlikteki yemleri al
        cursor.execute("SELECT * FROM feeds WHERE farm_id = ?", (farm_id,))
        feeds = cursor.fetchall()

        if len(feeds) < 2:
            raise HTTPException(status_code=400, detail="En az 2 yem gerekli")

        # Basit optimizasyon: En ucuz konsantre + en ucuz kaba yem
        concentrates = [f for f in feeds if dict(f)['feed_type'] == 'concentrate']
        forages = [f for f in feeds if dict(f)['feed_type'] in ['hay', 'silage']]

        if not concentrates or not forages:
            raise HTTPException(status_code=400, detail="Hem konsantre hem kaba yem gerekli")

        best_concentrate = min(concentrates, key=lambda f: dict(f)['cost_per_kg'])
        best_forage = min(forages, key=lambda f: dict(f)['cost_per_kg'])

        # Basit rasyon: %30 konsantre, %70 kaba yem
        animal_dict = dict(animal)
        target_dmi = animal_dict['current_weight_kg'] * 0.025

        conc_amount = target_dmi * 0.3
        forage_amount = target_dmi * 0.7

        conc_dict = dict(best_concentrate)
        forage_dict = dict(best_forage)

        total_cost = (conc_amount * conc_dict['cost_per_kg']) + (forage_amount * forage_dict['cost_per_kg'])

        # Rasyon kaydını oluştur
        ration_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO rations (
                id, farm_id, animal_id, name, ration_type,
                total_cost_per_day, total_dry_matter_kg,
                total_crude_protein_percentage, total_metabolizable_energy_mcal,
                total_calcium_percentage, total_phosphorus_percentage,
                is_optimized, optimization_objective
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            ration_id, farm_id, animal_id, name, 'individual',
            total_cost, target_dmi, 15.0, 2.5, 0.6, 0.4, True, 'cost'
        ))

        # Bileşenleri ekle
        comp1_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO ration_components (
                id, ration_id, feed_id, amount_kg_per_day, percentage_of_total_dm,
                dry_matter_contribution_kg, protein_contribution_kg, energy_contribution_mcal,
                calcium_contribution_kg, phosphorus_contribution_kg, cost_contribution
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            comp1_id, ration_id, conc_dict['id'], conc_amount, 30.0,
            conc_amount * 0.88, conc_amount * 0.18, conc_amount * 2.8,
            conc_amount * 0.008, conc_amount * 0.004, conc_amount * conc_dict['cost_per_kg']
        ))

        comp2_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO ration_components (
                id, ration_id, feed_id, amount_kg_per_day, percentage_of_total_dm,
                dry_matter_contribution_kg, protein_contribution_kg, energy_contribution_mcal,
                calcium_contribution_kg, phosphorus_contribution_kg, cost_contribution
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            comp2_id, ration_id, forage_dict['id'], forage_amount, 70.0,
            forage_amount * 0.85, forage_amount * 0.08, forage_amount * 2.0,
            forage_amount * 0.004, forage_amount * 0.002, forage_amount * forage_dict['cost_per_kg']
        ))

        conn.commit()
        conn.close()

        return {
            "ration_id": ration_id,
            "message": "Rasyon başarıyla optimize edildi",
            "optimization_result": {
                "total_cost_per_day": round(total_cost, 2),
                "total_dry_matter_kg": round(target_dmi, 2),
                "components": [
                    {
                        "feed_name": conc_dict['name'],
                        "amount_kg": round(conc_amount, 2),
                        "cost": round(conc_amount * conc_dict['cost_per_kg'], 2)
                    },
                    {
                        "feed_name": forage_dict['name'],
                        "amount_kg": round(forage_amount, 2),
                        "cost": round(forage_amount * forage_dict['cost_per_kg'], 2)
                    }
                ],
                "adequacy_ratios": {
                    "protein_adequacy": 1.0,
                    "energy_adequacy": 1.0,
                    "calcium_adequacy": 1.0,
                    "phosphorus_adequacy": 1.0
                }
            }
        }

    except Exception as e:
        print(f"❌ Rasyon optimizasyon hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Optimizasyon hatası: {str(e)}")

@app.get("/api/rations/{ration_id}")
async def get_ration_details(ration_id: str):
    """Rasyon detaylarını getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Rasyon bilgilerini al
        cursor.execute("SELECT * FROM rations WHERE id = ?", (ration_id,))
        ration = cursor.fetchone()

        if not ration:
            raise HTTPException(status_code=404, detail="Rasyon bulunamadı")

        ration_dict = dict(ration)

        # Hayvan bilgilerini al (eğer varsa)
        animal_info = None
        if ration_dict['animal_id']:
            cursor.execute("SELECT * FROM animals WHERE id = ?", (ration_dict['animal_id'],))
            animal = cursor.fetchone()
            if animal:
                animal_dict = dict(animal)
                # Yaş hesaplama (doğum tarihinden)
                from datetime import datetime
                birth_date = datetime.strptime(animal_dict['birth_date'], '%Y-%m-%d')
                age_months = int((datetime.now() - birth_date).days / 30.44)

                animal_info = {
                    "id": animal_dict['id'],
                    "breed": animal_dict['breed'],
                    "gender": animal_dict['gender'],
                    "current_weight_kg": animal_dict['current_weight_kg'],
                    "age_months": age_months,
                    "body_condition_score": animal_dict['body_condition_score'],
                    "is_pregnant": animal_dict.get('is_pregnant', False)
                }

        # Rasyon bileşenlerini al
        cursor.execute("""
            SELECT rc.*, f.name as feed_name, f.feed_type, f.cost_per_kg,
                   f.dry_matter_percentage, f.crude_protein_percentage,
                   f.metabolizable_energy_mcal_kg
            FROM ration_components rc
            JOIN feeds f ON rc.feed_id = f.id
            WHERE rc.ration_id = ?
            ORDER BY rc.percentage_of_total_dm DESC
        """, (ration_id,))

        components_raw = cursor.fetchall()
        components = []

        for comp in components_raw:
            comp_dict = dict(comp)
            components.append({
                "id": comp_dict['id'],
                "feed_name": comp_dict['feed_name'],
                "feed_type": comp_dict['feed_type'],
                "amount_kg_per_day": comp_dict['amount_kg_per_day'],
                "percentage_of_total_dm": comp_dict['percentage_of_total_dm'],
                "dry_matter_contribution_kg": comp_dict['dry_matter_contribution_kg'],
                "protein_contribution_kg": comp_dict['protein_contribution_kg'],
                "energy_contribution_mcal": comp_dict['energy_contribution_mcal'],
                "calcium_contribution_kg": comp_dict['calcium_contribution_kg'],
                "phosphorus_contribution_kg": comp_dict['phosphorus_contribution_kg'],
                "cost_contribution": comp_dict['cost_contribution'],
                "feed_details": {
                    "cost_per_kg": comp_dict['cost_per_kg'],
                    "dry_matter_percentage": comp_dict['dry_matter_percentage'],
                    "crude_protein_percentage": comp_dict['crude_protein_percentage'],
                    "metabolizable_energy_mcal_kg": comp_dict['metabolizable_energy_mcal_kg'],
                    "calcium_percentage": 0.6,  # Varsayılan değer
                    "phosphorus_percentage": 0.4  # Varsayılan değer
                }
            })

        # Çiftlik bilgilerini al
        cursor.execute("SELECT name, location FROM farms WHERE id = ?", (ration_dict['farm_id'],))
        farm = cursor.fetchone()
        farm_info = dict(farm) if farm else {"name": "Bilinmeyen", "location": ""}

        conn.close()

        # Besin değeri analizleri hesapla
        total_dm = ration_dict['total_dry_matter_kg']
        nutritional_analysis = {
            "dry_matter_kg": total_dm,
            "crude_protein_kg": total_dm * (ration_dict['total_crude_protein_percentage'] / 100),
            "metabolizable_energy_mcal": ration_dict['total_metabolizable_energy_mcal'],
            "calcium_kg": total_dm * (ration_dict['total_calcium_percentage'] / 100),
            "phosphorus_kg": total_dm * (ration_dict['total_phosphorus_percentage'] / 100),
            "cost_per_day": ration_dict['total_cost_per_day'],
            "cost_per_kg_dm": ration_dict['total_cost_per_day'] / total_dm if total_dm > 0 else 0,
            "protein_percentage": ration_dict['total_crude_protein_percentage'],
            "energy_density_mcal_kg": ration_dict['total_metabolizable_energy_mcal'] / total_dm if total_dm > 0 else 0
        }

        # Aylık ve yıllık maliyet hesapla
        daily_cost = ration_dict['total_cost_per_day']
        cost_analysis = {
            "daily_cost": daily_cost,
            "weekly_cost": daily_cost * 7,
            "monthly_cost": daily_cost * 30,
            "yearly_cost": daily_cost * 365
        }

        return {
            "ration": {
                "id": ration_dict['id'],
                "farm_id": ration_dict['farm_id'],
                "animal_id": ration_dict['animal_id'],
                "name": ration_dict['name'],
                "description": ration_dict['description'],
                "ration_type": ration_dict['ration_type'],
                "target_group": ration_dict['target_group'],
                "is_optimized": ration_dict['is_optimized'],
                "optimization_objective": ration_dict['optimization_objective'],
                "optimization_score": ration_dict['optimization_score'],
                "is_active": ration_dict['is_active'],
                "created_at": ration_dict['created_at']
            },
            "farm_info": farm_info,
            "animal_info": animal_info,
            "components": components,
            "nutritional_analysis": nutritional_analysis,
            "cost_analysis": cost_analysis,
            "summary": {
                "total_components": len(components),
                "concentrate_percentage": sum(c['percentage_of_total_dm'] for c in components if c['feed_type'] == 'concentrate'),
                "forage_percentage": sum(c['percentage_of_total_dm'] for c in components if c['feed_type'] in ['hay', 'silage', 'pasture']),
                "most_expensive_component": max(components, key=lambda x: x['cost_contribution'])['feed_name'] if components else None,
                "protein_source": max(components, key=lambda x: x['protein_contribution_kg'])['feed_name'] if components else None
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Rasyon detay hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Rasyon detayları alınamadı: {str(e)}")

@app.put("/api/rations/{ration_id}/activate")
async def activate_ration(ration_id: str):
    """Rasyonu aktif hale getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Rasyon var mı kontrol et
        cursor.execute("SELECT animal_id FROM rations WHERE id = ?", (ration_id,))
        ration = cursor.fetchone()

        if not ration:
            raise HTTPException(status_code=404, detail="Rasyon bulunamadı")

        ration_dict = dict(ration)

        # Aynı hayvan için diğer rasyonları pasif yap
        if ration_dict['animal_id']:
            cursor.execute(
                "UPDATE rations SET is_active = 0 WHERE animal_id = ? AND id != ?",
                (ration_dict['animal_id'], ration_id)
            )

        # Bu rasyonu aktif yap
        cursor.execute("UPDATE rations SET is_active = 1 WHERE id = ?", (ration_id,))

        conn.commit()
        conn.close()

        return {"message": "Rasyon aktif hale getirildi"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Rasyon aktif etme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Rasyon aktif edilemedi: {str(e)}")

@app.delete("/api/rations/{ration_id}")
async def delete_ration(ration_id: str):
    """Rasyonu sil"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Rasyon var mı kontrol et
        cursor.execute("SELECT id FROM rations WHERE id = ?", (ration_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Rasyon bulunamadı")

        # Önce bileşenleri sil
        cursor.execute("DELETE FROM ration_components WHERE ration_id = ?", (ration_id,))

        # Sonra rasyonu sil
        cursor.execute("DELETE FROM rations WHERE id = ?", (ration_id,))

        conn.commit()
        conn.close()

        return {"message": "Rasyon başarıyla silindi"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Rasyon silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Rasyon silinemedi: {str(e)}")

# Simülasyon API endpoint'leri
@app.post("/api/simulations/run")
async def run_simulation(request_data: dict):
    """Yaşam boyu simülasyon çalıştır"""
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        from simulation_engine import LifetimeSimulationEngine

        animal_id = request_data.get('animal_id')
        farm_id = request_data.get('farm_id')
        simulation_name = request_data.get('name', 'Yaşam Boyu Simülasyon')
        end_age_months = request_data.get('end_age_months', 60)
        slaughter_age_months = request_data.get('slaughter_age_months')

        if not animal_id or not farm_id:
            raise HTTPException(status_code=400, detail="Hayvan ID ve Çiftlik ID gerekli")

        # Simülasyon motoru (doğru veritabanı yolu ile)
        engine = LifetimeSimulationEngine("hayvancilik.db")
        simulation_id = engine.run_lifetime_simulation(
            animal_id, farm_id, simulation_name, end_age_months, slaughter_age_months
        )

        return {
            "simulation_id": simulation_id,
            "message": "Simülasyon başarıyla tamamlandı",
            "status": "completed"
        }

    except Exception as e:
        print(f"❌ Simülasyon hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Simülasyon hatası: {str(e)}")

@app.get("/api/simulations/farm/{farm_id}")
async def get_simulations_by_farm(farm_id: str):
    """Çiftlikteki simülasyonları listele"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.*, a.breed, a.gender, a.current_weight_kg
            FROM simulations s
            JOIN animals a ON s.animal_id = a.id
            WHERE s.farm_id = ?
            ORDER BY s.created_at DESC
        """, (farm_id,))

        simulations = cursor.fetchall()
        conn.close()

        return [dict(sim) for sim in simulations]

    except Exception as e:
        print(f"❌ Simülasyon listesi hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Simülasyon listesi alınamadı: {str(e)}")

@app.get("/api/simulations/{simulation_id}")
async def get_simulation_details(simulation_id: str):
    """Simülasyon detaylarını getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Ana simülasyon bilgileri
        cursor.execute("""
            SELECT s.*, a.breed, a.gender, a.current_weight_kg, a.birth_date,
                   f.name as farm_name, f.location as farm_location
            FROM simulations s
            JOIN animals a ON s.animal_id = a.id
            JOIN farms f ON s.farm_id = f.id
            WHERE s.id = ?
        """, (simulation_id,))

        simulation = cursor.fetchone()
        if not simulation:
            raise HTTPException(status_code=404, detail="Simülasyon bulunamadı")

        simulation_dict = dict(simulation)

        # Simülasyon detayları
        cursor.execute("""
            SELECT * FROM simulation_details
            WHERE simulation_id = ?
            ORDER BY month
        """, (simulation_id,))

        details = cursor.fetchall()
        details_list = [dict(detail) for detail in details]

        conn.close()

        # Özet istatistikler hesapla
        if details_list:
            total_months = len(details_list)
            final_weight = details_list[-1]['weight_kg']
            initial_weight = details_list[0]['weight_kg']
            total_weight_gain = final_weight - initial_weight
            avg_daily_gain = sum(d['daily_gain_kg'] for d in details_list) / total_months
            avg_daily_consumption = sum(d['feed_consumption_kg_per_day'] for d in details_list) / total_months

            # Dönemsel analiz
            stages = {}
            for detail in details_list:
                stage = detail['physiological_stage']
                if stage not in stages:
                    stages[stage] = {
                        'months': 0,
                        'total_cost': 0,
                        'total_consumption': 0
                    }
                stages[stage]['months'] += 1
                stages[stage]['total_cost'] += detail['monthly_feed_cost']
                stages[stage]['total_consumption'] += detail['monthly_feed_consumption_kg']

            summary = {
                'total_months': total_months,
                'initial_weight_kg': initial_weight,
                'final_weight_kg': final_weight,
                'total_weight_gain_kg': round(total_weight_gain, 1),
                'average_daily_gain_kg': round(avg_daily_gain, 3),
                'average_daily_consumption_kg': round(avg_daily_consumption, 2),
                'total_feed_cost': simulation_dict['total_feed_cost'],
                'total_feed_consumption_kg': simulation_dict['total_feed_consumption_kg'],
                'cost_per_kg_gain': round(simulation_dict['total_feed_cost'] / total_weight_gain, 2) if total_weight_gain > 0 else 0,
                'feed_conversion_ratio': round(simulation_dict['total_feed_consumption_kg'] / total_weight_gain, 2) if total_weight_gain > 0 else 0,
                'stages_analysis': stages
            }
        else:
            summary = {}

        return {
            'simulation': simulation_dict,
            'details': details_list,
            'summary': summary
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Simülasyon detay hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Simülasyon detayları alınamadı: {str(e)}")

@app.delete("/api/simulations/{simulation_id}")
async def delete_simulation(simulation_id: str):
    """Simülasyonu sil"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Simülasyon var mı kontrol et
        cursor.execute("SELECT id FROM simulations WHERE id = ?", (simulation_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Simülasyon bulunamadı")

        # Önce detayları sil
        cursor.execute("DELETE FROM simulation_details WHERE simulation_id = ?", (simulation_id,))

        # Sonra ana kaydı sil
        cursor.execute("DELETE FROM simulations WHERE id = ?", (simulation_id,))

        conn.commit()
        conn.close()

        return {"message": "Simülasyon başarıyla silindi"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Simülasyon silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Simülasyon silinemedi: {str(e)}")

# Dashboard API endpoint'leri
@app.get("/api/dashboard/overview/{farm_id}")
async def get_dashboard_overview(farm_id: str):
    """Dashboard genel bakış verilerini getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Hayvan istatistikleri
        cursor.execute("""
            SELECT
                COUNT(*) as total_animals,
                COUNT(CASE WHEN gender = 'male' THEN 1 END) as male_count,
                COUNT(CASE WHEN gender = 'female' THEN 1 END) as female_count,
                AVG(current_weight_kg) as avg_weight,
                AVG(CAST((julianday('now') - julianday(birth_date)) / 30.44 AS INTEGER)) as avg_age_months
            FROM animals
            WHERE farm_id = ?
        """, (farm_id,))
        animal_stats = dict(cursor.fetchone())

        # Yem istatistikleri
        cursor.execute("""
            SELECT
                COUNT(*) as total_feeds,
                AVG(cost_per_kg) as avg_cost_per_kg,
                SUM(CASE WHEN feed_type = 'concentrate' THEN 1 ELSE 0 END) as concentrate_count,
                SUM(CASE WHEN feed_type = 'hay' THEN 1 ELSE 0 END) as hay_count,
                SUM(CASE WHEN feed_type = 'silage' THEN 1 ELSE 0 END) as silage_count
            FROM feeds
            WHERE farm_id = ?
        """, (farm_id,))
        feed_stats = dict(cursor.fetchone())

        # Rasyon istatistikleri
        cursor.execute("""
            SELECT
                COUNT(*) as total_rations,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_rations,
                COUNT(CASE WHEN is_optimized = 1 THEN 1 END) as optimized_rations
            FROM rations
            WHERE farm_id = ?
        """, (farm_id,))
        ration_stats = dict(cursor.fetchone())

        # Simülasyon istatistikleri
        cursor.execute("""
            SELECT
                COUNT(*) as total_simulations,
                AVG(total_feed_cost) as avg_simulation_cost,
                AVG(total_feed_consumption_kg) as avg_feed_consumption
            FROM simulations
            WHERE farm_id = ?
        """, (farm_id,))
        simulation_stats = dict(cursor.fetchone())

        # Son 30 günde oluşturulan kayıtlar
        cursor.execute("""
            SELECT
                COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as new_animals_30d
            FROM animals
            WHERE farm_id = ?
        """, (farm_id,))
        recent_animals = cursor.fetchone()[0]

        cursor.execute("""
            SELECT
                COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as new_rations_30d
            FROM rations
            WHERE farm_id = ?
        """, (farm_id,))
        recent_rations = cursor.fetchone()[0]

        cursor.execute("""
            SELECT
                COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as new_simulations_30d
            FROM simulations
            WHERE farm_id = ?
        """, (farm_id,))
        recent_simulations = cursor.fetchone()[0]

        conn.close()

        return {
            "animal_stats": animal_stats,
            "feed_stats": feed_stats,
            "ration_stats": ration_stats,
            "simulation_stats": simulation_stats,
            "recent_activity": {
                "new_animals_30d": recent_animals,
                "new_rations_30d": recent_rations,
                "new_simulations_30d": recent_simulations
            }
        }

    except Exception as e:
        print(f"❌ Dashboard genel bakış hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Dashboard verileri alınamadı: {str(e)}")

@app.get("/api/dashboard/financial/{farm_id}")
async def get_dashboard_financial(farm_id: str):
    """Dashboard finansal verilerini getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Aktif rasyonların günlük maliyeti
        cursor.execute("""
            SELECT
                r.id,
                r.name,
                SUM(rc.cost_contribution) as daily_cost,
                SUM(rc.dry_matter_contribution_kg) as daily_dm_kg
            FROM rations r
            JOIN ration_components rc ON r.id = rc.ration_id
            WHERE r.farm_id = ? AND r.is_active = 1
            GROUP BY r.id, r.name
        """, (farm_id,))
        active_rations = cursor.fetchall()

        total_daily_cost = sum(ration['daily_cost'] for ration in active_rations)
        total_daily_dm = sum(ration['daily_dm_kg'] for ration in active_rations)

        # Yem türü bazında maliyet dağılımı
        cursor.execute("""
            SELECT
                f.feed_type,
                AVG(f.cost_per_kg) as avg_cost,
                COUNT(*) as feed_count
            FROM feeds f
            WHERE f.farm_id = ?
            GROUP BY f.feed_type
        """, (farm_id,))
        cost_by_feed_type = cursor.fetchall()

        # Simülasyon bazlı projeksiyonlar
        cursor.execute("""
            SELECT
                AVG(total_feed_cost) as avg_lifetime_cost,
                AVG(total_feed_consumption_kg) as avg_lifetime_consumption,
                COUNT(*) as simulation_count
            FROM simulations
            WHERE farm_id = ?
        """, (farm_id,))
        simulation_projections = dict(cursor.fetchone())

        conn.close()

        return {
            "daily_costs": {
                "total_daily_cost": round(total_daily_cost, 2),
                "total_daily_dm_kg": round(total_daily_dm, 2),
                "weekly_cost": round(total_daily_cost * 7, 2),
                "monthly_cost": round(total_daily_cost * 30.44, 2),
                "yearly_cost": round(total_daily_cost * 365, 2)
            },
            "active_rations": [dict(ration) for ration in active_rations],
            "cost_by_feed_type": [dict(item) for item in cost_by_feed_type],
            "projections": simulation_projections
        }

    except Exception as e:
        print(f"❌ Dashboard finansal hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Finansal veriler alınamadı: {str(e)}")

@app.get("/api/dashboard/performance/{farm_id}")
async def get_dashboard_performance(farm_id: str):
    """Dashboard performans verilerini getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Hayvan performans metrikleri
        cursor.execute("""
            SELECT
                breed,
                COUNT(*) as animal_count,
                AVG(current_weight_kg) as avg_weight,
                AVG(body_condition_score) as avg_bcs
            FROM animals
            WHERE farm_id = ?
            GROUP BY breed
        """, (farm_id,))
        breed_performance = cursor.fetchall()

        # Simülasyon performans metrikleri
        cursor.execute("""
            SELECT
                s.id,
                s.name,
                s.total_feed_cost,
                s.total_feed_consumption_kg,
                sd.total_months,
                sd.total_weight_gain_kg,
                sd.average_daily_gain_kg,
                sd.feed_conversion_ratio,
                sd.cost_per_kg_gain
            FROM simulations s
            JOIN (
                SELECT
                    simulation_id,
                    COUNT(*) as total_months,
                    MAX(weight_kg) - MIN(weight_kg) as total_weight_gain_kg,
                    AVG(daily_gain_kg) as average_daily_gain_kg,
                    SUM(monthly_feed_consumption_kg) / (MAX(weight_kg) - MIN(weight_kg)) as feed_conversion_ratio,
                    SUM(monthly_feed_cost) / (MAX(weight_kg) - MIN(weight_kg)) as cost_per_kg_gain
                FROM simulation_details
                GROUP BY simulation_id
            ) sd ON s.id = sd.simulation_id
            WHERE s.farm_id = ?
            ORDER BY s.created_at DESC
            LIMIT 10
        """, (farm_id,))
        recent_simulations = cursor.fetchall()

        # En iyi performans gösteren simülasyonlar
        cursor.execute("""
            SELECT
                s.name,
                sd.feed_conversion_ratio,
                sd.cost_per_kg_gain,
                sd.average_daily_gain_kg
            FROM simulations s
            JOIN (
                SELECT
                    simulation_id,
                    AVG(daily_gain_kg) as average_daily_gain_kg,
                    SUM(monthly_feed_consumption_kg) / (MAX(weight_kg) - MIN(weight_kg)) as feed_conversion_ratio,
                    SUM(monthly_feed_cost) / (MAX(weight_kg) - MIN(weight_kg)) as cost_per_kg_gain
                FROM simulation_details
                GROUP BY simulation_id
                HAVING MAX(weight_kg) - MIN(weight_kg) > 0
            ) sd ON s.id = sd.simulation_id
            WHERE s.farm_id = ?
            ORDER BY sd.cost_per_kg_gain ASC
            LIMIT 5
        """, (farm_id,))
        best_performers = cursor.fetchall()

        conn.close()

        return {
            "breed_performance": [dict(item) for item in breed_performance],
            "recent_simulations": [dict(item) for item in recent_simulations],
            "best_performers": [dict(item) for item in best_performers]
        }

    except Exception as e:
        print(f"❌ Dashboard performans hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Performans verileri alınamadı: {str(e)}")

# Sağlık Yönetimi API endpoint'leri
@app.get("/api/health/vaccines/schedule/{animal_id}")
async def get_vaccine_schedule(animal_id: str):
    """Hayvan için aşı takvimini getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Hayvan bilgilerini al
        cursor.execute("SELECT breed, birth_date FROM animals WHERE id = ?", (animal_id,))
        animal = cursor.fetchone()
        if not animal:
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        animal_dict = dict(animal)
        breed = animal_dict['breed']
        birth_date = datetime.strptime(animal_dict['birth_date'], '%Y-%m-%d')
        current_age_months = int((datetime.now() - birth_date).days / 30.44)

        # Aşı şemasını getir (breed + all)
        cursor.execute("""
            SELECT * FROM vaccine_schedules
            WHERE breed IN (?, 'all')
            ORDER BY age_months_min, vaccine_name
        """, (breed,))
        schedules = cursor.fetchall()

        # Yapılan aşıları getir
        cursor.execute("""
            SELECT vaccine_name, vaccination_date, next_due_date
            FROM animal_vaccinations
            WHERE animal_id = ?
            ORDER BY vaccination_date DESC
        """, (animal_id,))
        vaccinations = cursor.fetchall()

        # Aşı durumunu hesapla
        vaccine_status = []
        for schedule in schedules:
            schedule_dict = dict(schedule)
            vaccine_name = schedule_dict['vaccine_name']

            # Bu aşı yapıldı mı?
            last_vaccination = None
            for vacc in vaccinations:
                if vacc['vaccine_name'] == vaccine_name:
                    last_vaccination = vacc
                    break

            # Durum hesaplama
            status = "pending"
            due_date = None
            days_overdue = 0

            if current_age_months >= schedule_dict['age_months_min']:
                if last_vaccination:
                    last_date = datetime.strptime(last_vaccination['vaccination_date'], '%Y-%m-%d')
                    if schedule_dict['repeat_interval_months']:
                        next_due = last_date + timedelta(days=schedule_dict['repeat_interval_months'] * 30.44)
                        due_date = next_due.strftime('%Y-%m-%d')
                        if datetime.now() > next_due:
                            status = "overdue"
                            days_overdue = (datetime.now() - next_due).days
                        else:
                            status = "completed"
                    else:
                        status = "completed"
                else:
                    # Hiç yapılmamış
                    if schedule_dict['age_months_max'] and current_age_months > schedule_dict['age_months_max']:
                        status = "overdue"
                        ideal_date = birth_date + timedelta(days=schedule_dict['age_months_max'] * 30.44)
                        days_overdue = (datetime.now() - ideal_date).days
                    else:
                        status = "due"
                        ideal_date = birth_date + timedelta(days=schedule_dict['age_months_min'] * 30.44)
                        due_date = ideal_date.strftime('%Y-%m-%d')

            vaccine_status.append({
                **schedule_dict,
                'status': status,
                'due_date': due_date,
                'days_overdue': days_overdue,
                'last_vaccination': dict(last_vaccination) if last_vaccination else None
            })

        conn.close()
        return {
            'animal_id': animal_id,
            'current_age_months': current_age_months,
            'vaccine_schedule': vaccine_status
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Aşı takvimi hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Aşı takvimi alınamadı: {str(e)}")

@app.post("/api/health/vaccines/record")
async def record_vaccination(request_data: dict):
    """Aşı kaydı oluştur"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        vaccination_id = str(uuid.uuid4())
        animal_id = request_data.get('animal_id')
        vaccine_name = request_data.get('vaccine_name')
        vaccination_date = request_data.get('vaccination_date')
        veterinarian = request_data.get('veterinarian', '')
        batch_number = request_data.get('batch_number', '')
        cost = request_data.get('cost', 0)
        notes = request_data.get('notes', '')

        # Sonraki aşı tarihini hesapla
        cursor.execute("""
            SELECT repeat_interval_months FROM vaccine_schedules
            WHERE vaccine_name = ? LIMIT 1
        """, (vaccine_name,))
        schedule = cursor.fetchone()

        next_due_date = None
        if schedule and schedule['repeat_interval_months']:
            vacc_date = datetime.strptime(vaccination_date, '%Y-%m-%d')
            next_due = vacc_date + timedelta(days=schedule['repeat_interval_months'] * 30.44)
            next_due_date = next_due.strftime('%Y-%m-%d')

        # Aşı kaydını ekle
        cursor.execute('''
            INSERT INTO animal_vaccinations (
                id, animal_id, vaccine_name, vaccination_date, next_due_date,
                veterinarian, batch_number, cost, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (vaccination_id, animal_id, vaccine_name, vaccination_date, next_due_date,
              veterinarian, batch_number, cost, notes))

        # Hatırlatma oluştur (eğer tekrar aşı gerekiyorsa)
        if next_due_date:
            reminder_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO reminders (
                    id, animal_id, reminder_type, title, description, due_date, priority
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (reminder_id, animal_id, 'vaccination', f'{vaccine_name} Aşısı',
                  f'{vaccine_name} aşısının tekrar zamanı geldi', next_due_date, 'high'))

        conn.commit()
        conn.close()

        return {
            "vaccination_id": vaccination_id,
            "message": "Aşı kaydı başarıyla oluşturuldu",
            "next_due_date": next_due_date
        }

    except Exception as e:
        print(f"❌ Aşı kayıt hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Aşı kaydedilemedi: {str(e)}")

@app.get("/api/health/records/{animal_id}")
async def get_health_records(animal_id: str):
    """Hayvan sağlık kayıtlarını getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM health_records
            WHERE animal_id = ?
            ORDER BY start_date DESC
        """, (animal_id,))

        records = cursor.fetchall()
        conn.close()

        return [dict(record) for record in records]

    except Exception as e:
        print(f"❌ Sağlık kayıtları hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Sağlık kayıtları alınamadı: {str(e)}")

@app.post("/api/health/records")
async def create_health_record(request_data: dict):
    """Sağlık kaydı oluştur"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        record_id = str(uuid.uuid4())
        animal_id = request_data.get('animal_id')
        record_type = request_data.get('record_type')
        diagnosis = request_data.get('diagnosis', '')
        treatment = request_data.get('treatment', '')
        medication = request_data.get('medication', '')
        veterinarian = request_data.get('veterinarian', '')
        cost = request_data.get('cost', 0)
        start_date = request_data.get('start_date')
        end_date = request_data.get('end_date')
        recovery_status = request_data.get('recovery_status', 'ongoing')
        notes = request_data.get('notes', '')

        cursor.execute('''
            INSERT INTO health_records (
                id, animal_id, record_type, diagnosis, treatment, medication,
                veterinarian, cost, start_date, end_date, recovery_status, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (record_id, animal_id, record_type, diagnosis, treatment, medication,
              veterinarian, cost, start_date, end_date, recovery_status, notes))

        # Takip hatırlatması oluştur (eğer devam eden tedavi varsa)
        if recovery_status == 'ongoing' and end_date:
            reminder_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO reminders (
                    id, animal_id, reminder_type, title, description, due_date, priority
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (reminder_id, animal_id, 'health_checkup', 'Sağlık Kontrolü',
                  f'{diagnosis} tedavi takibi', end_date, 'medium'))

        conn.commit()
        conn.close()

        return {
            "record_id": record_id,
            "message": "Sağlık kaydı başarıyla oluşturuldu"
        }

    except Exception as e:
        print(f"❌ Sağlık kaydı hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Sağlık kaydı oluşturulamadı: {str(e)}")

# Üreme Yönetimi API endpoint'leri
@app.get("/api/breeding/records/{animal_id}")
async def get_breeding_records(animal_id: str):
    """Hayvan üreme kayıtlarını getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT br.*,
                   male.tag as male_tag,
                   cr.calving_date,
                   cr.birth_weight,
                   cr.calving_ease
            FROM breeding_records br
            LEFT JOIN animals male ON br.male_id = male.id
            LEFT JOIN calving_records cr ON br.female_id = cr.mother_id
                AND date(br.expected_calving_date) = date(cr.calving_date)
            WHERE br.female_id = ?
            ORDER BY br.breeding_date DESC
        """, (animal_id,))

        records = cursor.fetchall()
        conn.close()

        return [dict(record) for record in records]

    except Exception as e:
        print(f"❌ Üreme kayıtları hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Üreme kayıtları alınamadı: {str(e)}")

@app.post("/api/breeding/records")
async def create_breeding_record(request_data: dict):
    """Çiftleşme kaydı oluştur"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        record_id = str(uuid.uuid4())
        female_id = request_data.get('female_id')
        male_id = request_data.get('male_id')
        breeding_date = request_data.get('breeding_date')
        breeding_type = request_data.get('breeding_type')
        semen_source = request_data.get('semen_source', '')
        veterinarian = request_data.get('veterinarian', '')
        cost = request_data.get('cost', 0)
        notes = request_data.get('notes', '')

        # Gebelik süresini al (ırk özelliklerinden)
        cursor.execute("""
            SELECT bc.gestation_length_days
            FROM animals a
            JOIN breed_characteristics bc ON a.breed = bc.breed
            WHERE a.id = ?
        """, (female_id,))
        breed_info = cursor.fetchone()

        gestation_days = 283  # Varsayılan
        if breed_info:
            gestation_days = breed_info['gestation_length_days']

        # Beklenen doğum tarihini hesapla
        breeding_dt = datetime.strptime(breeding_date, '%Y-%m-%d')
        expected_calving = breeding_dt + timedelta(days=gestation_days)
        expected_calving_date = expected_calving.strftime('%Y-%m-%d')

        cursor.execute('''
            INSERT INTO breeding_records (
                id, female_id, male_id, breeding_date, breeding_type, semen_source,
                expected_calving_date, veterinarian, cost, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (record_id, female_id, male_id, breeding_date, breeding_type, semen_source,
              expected_calving_date, veterinarian, cost, notes))

        # Gebelik kontrolü hatırlatması oluştur (45 gün sonra)
        pregnancy_check_date = breeding_dt + timedelta(days=45)
        reminder_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO reminders (
                id, animal_id, reminder_type, title, description, due_date, priority
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (reminder_id, female_id, 'pregnancy_check', 'Gebelik Kontrolü',
              'Çiftleşmeden 45 gün sonra gebelik kontrolü', pregnancy_check_date.strftime('%Y-%m-%d'), 'high'))

        # Doğum hatırlatması oluştur
        calving_reminder_date = expected_calving - timedelta(days=7)  # 1 hafta önceden
        reminder_id2 = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO reminders (
                id, animal_id, reminder_type, title, description, due_date, priority
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (reminder_id2, female_id, 'calving_preparation', 'Doğum Hazırlığı',
              f'Beklenen doğum tarihi: {expected_calving_date}', calving_reminder_date.strftime('%Y-%m-%d'), 'high'))

        conn.commit()
        conn.close()

        return {
            "breeding_id": record_id,
            "expected_calving_date": expected_calving_date,
            "message": "Çiftleşme kaydı başarıyla oluşturuldu"
        }

    except Exception as e:
        print(f"❌ Çiftleşme kaydı hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Çiftleşme kaydı oluşturulamadı: {str(e)}")

@app.put("/api/breeding/pregnancy/{breeding_id}")
async def update_pregnancy_status(breeding_id: str, request_data: dict):
    """Gebelik durumunu güncelle"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        pregnancy_confirmed = request_data.get('pregnancy_confirmed')
        pregnancy_check_date = request_data.get('pregnancy_check_date')
        pregnancy_check_method = request_data.get('pregnancy_check_method', '')
        veterinarian = request_data.get('veterinarian', '')

        cursor.execute('''
            UPDATE breeding_records
            SET pregnancy_confirmed = ?, pregnancy_check_date = ?,
                pregnancy_check_method = ?, veterinarian = ?
            WHERE id = ?
        ''', (pregnancy_confirmed, pregnancy_check_date, pregnancy_check_method, veterinarian, breeding_id))

        # Eğer gebelik negatifse, doğum hatırlatmasını sil
        if not pregnancy_confirmed:
            cursor.execute("""
                DELETE FROM reminders
                WHERE animal_id = (SELECT female_id FROM breeding_records WHERE id = ?)
                AND reminder_type = 'calving_preparation'
                AND due_date >= date('now')
            """, (breeding_id,))

        conn.commit()
        conn.close()

        return {
            "message": "Gebelik durumu güncellendi",
            "pregnancy_confirmed": pregnancy_confirmed
        }

    except Exception as e:
        print(f"❌ Gebelik güncelleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Gebelik durumu güncellenemedi: {str(e)}")

@app.post("/api/breeding/calving")
async def record_calving(request_data: dict):
    """Doğum kaydı oluştur"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        calving_id = str(uuid.uuid4())
        mother_id = request_data.get('mother_id')
        calf_id = request_data.get('calf_id')
        calving_date = request_data.get('calving_date')
        calving_ease = request_data.get('calving_ease', 1)
        birth_weight = request_data.get('birth_weight')
        complications = request_data.get('complications', '')
        veterinarian_assisted = request_data.get('veterinarian_assisted', False)
        veterinarian = request_data.get('veterinarian', '')
        cost = request_data.get('cost', 0)
        notes = request_data.get('notes', '')

        cursor.execute('''
            INSERT INTO calving_records (
                id, mother_id, calf_id, calving_date, calving_ease, birth_weight,
                complications, veterinarian_assisted, veterinarian, cost, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (calving_id, mother_id, calf_id, calving_date, calving_ease, birth_weight,
              complications, veterinarian_assisted, veterinarian, cost, notes))

        # Doğum sonrası kontrol hatırlatması (7 gün sonra)
        calving_dt = datetime.strptime(calving_date, '%Y-%m-%d')
        checkup_date = calving_dt + timedelta(days=7)
        reminder_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO reminders (
                id, animal_id, reminder_type, title, description, due_date, priority
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (reminder_id, mother_id, 'postpartum_checkup', 'Doğum Sonrası Kontrol',
              'Doğum sonrası sağlık kontrolü', checkup_date.strftime('%Y-%m-%d'), 'medium'))

        # Eğer buzağı kaydı varsa, buzağı için aşı hatırlatması oluştur
        if calf_id:
            # İlk aşı hatırlatması (1 ay sonra)
            first_vaccine_date = calving_dt + timedelta(days=30)
            reminder_id2 = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO reminders (
                    id, animal_id, reminder_type, title, description, due_date, priority
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (reminder_id2, calf_id, 'vaccination', 'İlk Aşı Zamanı',
                  'Buzağının ilk aşı zamanı geldi', first_vaccine_date.strftime('%Y-%m-%d'), 'high'))

        # Doğum hatırlatmalarını tamamlandı olarak işaretle
        cursor.execute("""
            UPDATE reminders
            SET is_completed = TRUE, completed_date = ?
            WHERE animal_id = ? AND reminder_type = 'calving_preparation'
            AND is_completed = FALSE
        """, (calving_date, mother_id))

        conn.commit()
        conn.close()

        return {
            "calving_id": calving_id,
            "message": "Doğum kaydı başarıyla oluşturuldu"
        }

    except Exception as e:
        print(f"❌ Doğum kaydı hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Doğum kaydı oluşturulamadı: {str(e)}")

@app.get("/api/breeding/calendar/{farm_id}")
async def get_breeding_calendar(farm_id: str):
    """Üreme takvimini getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Yaklaşan doğumlar (30 gün içinde)
        cursor.execute("""
            SELECT br.*, a.tag, a.breed
            FROM breeding_records br
            JOIN animals a ON br.female_id = a.id
            WHERE a.farm_id = ?
            AND br.pregnancy_confirmed = TRUE
            AND date(br.expected_calving_date) BETWEEN date('now') AND date('now', '+30 days')
            ORDER BY br.expected_calving_date
        """, (farm_id,))
        upcoming_calvings = cursor.fetchall()

        # Gebelik kontrolleri gereken hayvanlar
        cursor.execute("""
            SELECT br.*, a.tag, a.breed
            FROM breeding_records br
            JOIN animals a ON br.female_id = a.id
            WHERE a.farm_id = ?
            AND br.pregnancy_confirmed IS NULL
            AND date(br.breeding_date, '+45 days') <= date('now', '+7 days')
            ORDER BY br.breeding_date
        """, (farm_id,))
        pregnancy_checks = cursor.fetchall()

        # Çiftleşme için hazır hayvanlar (son doğumdan 60+ gün geçenler)
        cursor.execute("""
            SELECT a.*, cr.calving_date
            FROM animals a
            LEFT JOIN calving_records cr ON a.id = cr.mother_id
            WHERE a.farm_id = ?
            AND a.gender = 'female'
            AND (cr.calving_date IS NULL OR date(cr.calving_date, '+60 days') <= date('now'))
            AND a.id NOT IN (
                SELECT female_id FROM breeding_records
                WHERE pregnancy_confirmed IS NULL OR pregnancy_confirmed = TRUE
            )
            ORDER BY cr.calving_date
        """, (farm_id,))
        ready_for_breeding = cursor.fetchall()

        conn.close()

        return {
            'upcoming_calvings': [dict(record) for record in upcoming_calvings],
            'pregnancy_checks': [dict(record) for record in pregnancy_checks],
            'ready_for_breeding': [dict(record) for record in ready_for_breeding]
        }

    except Exception as e:
        print(f"❌ Üreme takvimi hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Üreme takvimi alınamadı: {str(e)}")

# Hatırlatma Sistemi API endpoint'leri
@app.get("/api/reminders/farm/{farm_id}")
async def get_farm_reminders(farm_id: str):
    """Çiftlik hatırlatmalarını getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT r.*, a.tag, a.breed, a.gender
            FROM reminders r
            JOIN animals a ON r.animal_id = a.id
            WHERE a.farm_id = ? AND r.is_completed = FALSE
            ORDER BY
                CASE r.priority
                    WHEN 'high' THEN 1
                    WHEN 'medium' THEN 2
                    WHEN 'low' THEN 3
                END,
                r.due_date ASC
        """, (farm_id,))

        reminders = cursor.fetchall()

        # Hatırlatmaları kategorilere ayır
        overdue = []
        today = []
        upcoming = []

        today_date = datetime.now().strftime('%Y-%m-%d')

        for reminder in reminders:
            reminder_dict = dict(reminder)
            due_date = reminder_dict['due_date']

            if due_date < today_date:
                reminder_dict['days_overdue'] = (datetime.now() - datetime.strptime(due_date, '%Y-%m-%d')).days
                overdue.append(reminder_dict)
            elif due_date == today_date:
                today.append(reminder_dict)
            else:
                reminder_dict['days_until'] = (datetime.strptime(due_date, '%Y-%m-%d') - datetime.now()).days
                upcoming.append(reminder_dict)

        conn.close()

        return {
            'overdue': overdue,
            'today': today,
            'upcoming': upcoming[:10],  # Sadece yaklaşan 10 hatırlatma
            'total_count': len(reminders)
        }

    except Exception as e:
        print(f"❌ Hatırlatmalar hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hatırlatmalar alınamadı: {str(e)}")

@app.post("/api/reminders")
async def create_reminder(request_data: dict):
    """Manuel hatırlatma oluştur"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        reminder_id = str(uuid.uuid4())
        animal_id = request_data.get('animal_id')
        reminder_type = request_data.get('reminder_type', 'custom')
        title = request_data.get('title')
        description = request_data.get('description', '')
        due_date = request_data.get('due_date')
        priority = request_data.get('priority', 'medium')

        cursor.execute('''
            INSERT INTO reminders (
                id, animal_id, reminder_type, title, description, due_date, priority
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (reminder_id, animal_id, reminder_type, title, description, due_date, priority))

        conn.commit()
        conn.close()

        return {
            "reminder_id": reminder_id,
            "message": "Hatırlatma başarıyla oluşturuldu"
        }

    except Exception as e:
        print(f"❌ Hatırlatma oluşturma hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hatırlatma oluşturulamadı: {str(e)}")

@app.get("/api/reminders/all")
async def get_all_reminders():
    """Tüm hatırlatmaları getir"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT r.*, a.tag, a.breed, a.gender, f.name as farm_name
            FROM reminders r
            JOIN animals a ON r.animal_id = a.id
            JOIN farms f ON a.farm_id = f.id
            ORDER BY
                CASE r.priority
                    WHEN 'high' THEN 1
                    WHEN 'medium' THEN 2
                    WHEN 'low' THEN 3
                END,
                r.due_date ASC
        """)

        reminders = cursor.fetchall()
        conn.close()

        return [dict(reminder) for reminder in reminders]

    except Exception as e:
        print(f"❌ Tüm hatırlatmalar hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hatırlatmalar alınamadı: {str(e)}")

@app.put("/api/reminders/{reminder_id}/complete")
async def complete_reminder(reminder_id: str):
    """Hatırlatmayı tamamla"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce hatırlatmanın var olup olmadığını kontrol et
        cursor.execute("SELECT title FROM reminders WHERE id = ?", (reminder_id,))
        reminder = cursor.fetchone()
        if not reminder:
            conn.close()
            raise HTTPException(status_code=404, detail="Hatırlatma bulunamadı")

        title = reminder['title']

        # Hatırlatmayı tamamla
        cursor.execute("""
            UPDATE reminders
            SET is_completed = TRUE, completed_date = datetime('now')
            WHERE id = ?
        """, (reminder_id,))

        conn.commit()
        conn.close()

        print(f"✓ Hatırlatma tamamlandı: {title}")
        return {"message": "Hatırlatma başarıyla tamamlandı"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Hatırlatma tamamlama hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hatırlatma tamamlanamadı: {str(e)}")

@app.delete("/api/reminders/{reminder_id}")
async def delete_reminder(reminder_id: str):
    """Hatırlatma sil"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce hatırlatmanın var olup olmadığını kontrol et
        cursor.execute("SELECT title FROM reminders WHERE id = ?", (reminder_id,))
        reminder = cursor.fetchone()
        if not reminder:
            conn.close()
            raise HTTPException(status_code=404, detail="Hatırlatma bulunamadı")

        title = reminder['title']

        # Hatırlatmayı sil
        cursor.execute("DELETE FROM reminders WHERE id = ?", (reminder_id,))
        conn.commit()
        conn.close()

        print(f"✓ Hatırlatma silindi: {title}")
        return {"message": "Hatırlatma başarıyla silindi"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Hatırlatma silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hatırlatma silinemedi: {str(e)}")

# Dashboard'a hatırlatma verisi ekle
@app.get("/api/dashboard/reminders/{farm_id}")
async def get_dashboard_reminders(farm_id: str):
    """Dashboard için hatırlatma özeti"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Bugünkü hatırlatmalar
        cursor.execute("""
            SELECT COUNT(*) as today_count
            FROM reminders r
            JOIN animals a ON r.animal_id = a.id
            WHERE a.farm_id = ? AND r.is_completed = FALSE
            AND date(r.due_date) = date('now')
        """, (farm_id,))
        today_count = cursor.fetchone()['today_count']

        # Geciken hatırlatmalar
        cursor.execute("""
            SELECT COUNT(*) as overdue_count
            FROM reminders r
            JOIN animals a ON r.animal_id = a.id
            WHERE a.farm_id = ? AND r.is_completed = FALSE
            AND date(r.due_date) < date('now')
        """, (farm_id,))
        overdue_count = cursor.fetchone()['overdue_count']

        # Yaklaşan hatırlatmalar (7 gün içinde)
        cursor.execute("""
            SELECT COUNT(*) as upcoming_count
            FROM reminders r
            JOIN animals a ON r.animal_id = a.id
            WHERE a.farm_id = ? AND r.is_completed = FALSE
            AND date(r.due_date) BETWEEN date('now', '+1 day') AND date('now', '+7 days')
        """, (farm_id,))
        upcoming_count = cursor.fetchone()['upcoming_count']

        # En yakın 5 hatırlatma
        cursor.execute("""
            SELECT r.*, a.tag, a.breed
            FROM reminders r
            JOIN animals a ON r.animal_id = a.id
            WHERE a.farm_id = ? AND r.is_completed = FALSE
            ORDER BY r.due_date ASC
            LIMIT 5
        """, (farm_id,))
        next_reminders = cursor.fetchall()

        conn.close()

        return {
            'today_count': today_count,
            'overdue_count': overdue_count,
            'upcoming_count': upcoming_count,
            'next_reminders': [dict(reminder) for reminder in next_reminders]
        }

    except Exception as e:
        print(f"❌ Dashboard hatırlatma hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Dashboard hatırlatmaları alınamadı: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
