'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Heart,
  Shield,
  Stethoscope,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Plus,
  Users,
  Activity
} from 'lucide-react';
import { farmApi, animalApi, healthApi, reminderApi } from '@/services/api';
import { Farm, Animal } from '@/types';

export default function HealthManagementPage() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('');
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [healthSummary, setHealthSummary] = useState<any>(null);
  const [reminders, setReminders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadFarmHealthData();
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (!selectedFarmId && farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    }
  };

  const loadFarmHealthData = async () => {
    try {
      setLoading(true);

      // Paralel olarak verileri yükle
      const [animalsData, remindersData] = await Promise.all([
        animalApi.getAnimalsByFarm(selectedFarmId).catch(() => []),
        reminderApi.getFarmReminders(selectedFarmId).catch(() => [])
      ]);

      setAnimals(Array.isArray(animalsData) ? animalsData : []);
      setReminders(Array.isArray(remindersData) ? remindersData : []);

      // Sağlık özetini hesapla
      calculateHealthSummary(
        Array.isArray(animalsData) ? animalsData : [],
        Array.isArray(remindersData) ? remindersData : []
      );

    } catch (err) {
      setError('Sağlık verileri yüklenirken hata oluştu');
      console.error('Error loading health data:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateHealthSummary = (animalsData: Animal[], remindersData: any[]) => {
    // Güvenli array kontrolü
    const safeAnimalsData = Array.isArray(animalsData) ? animalsData : [];
    const safeRemindersData = Array.isArray(remindersData) ? remindersData : [];

    const healthReminders = safeRemindersData.filter(r =>
      r && (r.reminder_type === 'vaccination' || r.reminder_type === 'health_check')
    );

    const overdueReminders = healthReminders.filter(r =>
      r && r.due_date && new Date(r.due_date) < new Date() && !r.is_completed
    );

    const upcomingReminders = healthReminders.filter(r => {
      if (!r || !r.due_date) return false;
      const dueDate = new Date(r.due_date);
      const today = new Date();
      const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      return dueDate >= today && dueDate <= weekFromNow && !r.is_completed;
    });

    setHealthSummary({
      totalAnimals: safeAnimalsData.length,
      healthyAnimals: safeAnimalsData.filter(a => a && a.status === 'healthy').length,
      sickAnimals: safeAnimalsData.filter(a => a && a.status === 'sick').length,
      overdueVaccinations: overdueReminders.length,
      upcomingVaccinations: upcomingReminders.length,
      totalReminders: healthReminders.length
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getReminderIcon = (type: string) => {
    switch (type) {
      case 'vaccination': return Shield;
      case 'health_check': return Stethoscope;
      case 'treatment': return Heart;
      default: return Calendar;
    }
  };

  if (loading && !healthSummary) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Sağlık verileri yükleniyor...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sağlık Yönetimi</h1>
          <p className="text-gray-600 mt-2">
            Hayvan sağlığı, aşı takvimleri ve veteriner işlemleri
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            href="/health/vaccines"
            className="flex items-center space-x-2 px-4 py-2 border border-green-600 text-green-600 rounded-md hover:bg-green-50 transition-colors"
          >
            <Shield className="h-4 w-4" />
            <span>Aşı Yönetimi</span>
          </Link>
          <Link
            href="/health/vaccines/add"
            className="btn-primary text-white px-4 py-2 rounded-md inline-flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Aşı Kaydı Ekle
          </Link>
        </div>
      </div>

      {/* Çiftlik Seçimi */}
      {farms.length > 0 && (
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Çiftlik:</label>
            <select
              value={selectedFarmId}
              onChange={(e) => setSelectedFarmId(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {farms.map((farm) => (
                <option key={farm.id} value={farm.id}>
                  {farm.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Sağlık Özeti */}
      {healthSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Toplam Hayvan */}
          <div className="content-overlay p-6 hover-glow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Toplam Hayvan</p>
                <p className="text-2xl font-bold text-gray-900">{healthSummary.totalAnimals}</p>
              </div>
            </div>
          </div>

          {/* Sağlıklı Hayvanlar */}
          <div className="content-overlay p-6 hover-glow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Sağlıklı</p>
                <p className="text-2xl font-bold text-gray-900">{healthSummary.healthyAnimals}</p>
              </div>
            </div>
          </div>

          {/* Hasta Hayvanlar */}
          <div className="content-overlay p-6 hover-glow">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Hasta</p>
                <p className="text-2xl font-bold text-gray-900">{healthSummary.sickAnimals}</p>
              </div>
            </div>
          </div>

          {/* Geciken Aşılar */}
          <div className="content-overlay p-6 hover-glow">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Geciken Aşılar</p>
                <p className="text-2xl font-bold text-gray-900">{healthSummary.overdueVaccinations}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Yaklaşan Hatırlatmalar */}
      <div className="content-overlay p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Yaklaşan Sağlık Hatırlatmaları</h2>
          <Link
            href="/reminders"
            className="text-green-600 hover:text-green-900 text-sm font-medium"
          >
            Tümünü Görüntüle →
          </Link>
        </div>

        {!Array.isArray(reminders) || reminders.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Hatırlatma yok</h3>
            <p className="text-gray-600">Şu anda bekleyen sağlık hatırlatması bulunmuyor.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {reminders.slice(0, 5).map((reminder) => {
              const Icon = getReminderIcon(reminder.reminder_type);
              const isOverdue = new Date(reminder.due_date) < new Date();

              return (
                <div
                  key={reminder.id}
                  className={`flex items-center justify-between p-4 rounded-lg border ${
                    isOverdue ? 'bg-red-50 border-red-200' : 'bg-white border-gray-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      isOverdue ? 'bg-red-100' : 'bg-blue-100'
                    }`}>
                      <Icon className={`h-5 w-5 ${
                        isOverdue ? 'text-red-600' : 'text-blue-600'
                      }`} />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{reminder.title}</h4>
                      <p className="text-sm text-gray-600">
                        {reminder.tag} - {new Date(reminder.due_date).toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${
                      getPriorityColor(reminder.priority)
                    }`}>
                      {reminder.priority === 'high' ? 'Yüksek' :
                       reminder.priority === 'medium' ? 'Orta' : 'Düşük'}
                    </span>
                    {isOverdue && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 border border-red-200">
                        Gecikmiş
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Hızlı Erişim */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link href="/health/vaccines" className="content-overlay p-6 hover-glow block">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Aşı Yönetimi</h3>
              <p className="text-gray-600 text-sm">Aşı takvimleri ve kayıtları</p>
            </div>
          </div>
        </Link>

        <Link href="/health/records" className="content-overlay p-6 hover-glow block">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-100 rounded-lg">
              <Stethoscope className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Sağlık Kayıtları</h3>
              <p className="text-gray-600 text-sm">Hastalık ve tedavi kayıtları</p>
            </div>
          </div>
        </Link>

        <Link href="/reminders" className="content-overlay p-6 hover-glow block">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Hatırlatmalar</h3>
              <p className="text-gray-600 text-sm">Tüm hatırlatmaları görüntüle</p>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
}
