{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Tüm hatırlatmaları getir\n  getAllReminders: async (): Promise<any[]> => {\n    const response = await api.get('/reminders/all');\n    return response.data;\n  },\n\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Hatırlatma sil\n  deleteReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.delete(`/reminders/${reminderId}`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/health/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { \n  Heart, \n  Shield, \n  Stethoscope, \n  Calendar,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Plus,\n  Users,\n  Activity\n} from 'lucide-react';\nimport { farmApi, animalApi, healthApi, reminderApi } from '@/services/api';\nimport { Farm, Animal } from '@/types';\n\nexport default function HealthManagementPage() {\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [healthSummary, setHealthSummary] = useState<any>(null);\n  const [reminders, setReminders] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadFarmHealthData();\n    }\n  }, [selectedFarmId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (!selectedFarmId && farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlik verileri yüklenirken hata oluştu');\n      console.error('Error loading farms:', err);\n    }\n  };\n\n  const loadFarmHealthData = async () => {\n    try {\n      setLoading(true);\n      \n      // Paralel olarak verileri yükle\n      const [animalsData, remindersData] = await Promise.all([\n        animalApi.getAnimalsByFarm(selectedFarmId).catch(() => []),\n        reminderApi.getFarmReminders(selectedFarmId).catch(() => [])\n      ]);\n      \n      setAnimals(animalsData);\n      setReminders(remindersData);\n      \n      // Sağlık özetini hesapla\n      calculateHealthSummary(animalsData, remindersData);\n      \n    } catch (err) {\n      setError('Sağlık verileri yüklenirken hata oluştu');\n      console.error('Error loading health data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateHealthSummary = (animalsData: Animal[], remindersData: any[]) => {\n    const healthReminders = remindersData.filter(r => \n      r.reminder_type === 'vaccination' || r.reminder_type === 'health_check'\n    );\n    \n    const overdueReminders = healthReminders.filter(r => \n      new Date(r.due_date) < new Date() && !r.is_completed\n    );\n    \n    const upcomingReminders = healthReminders.filter(r => {\n      const dueDate = new Date(r.due_date);\n      const today = new Date();\n      const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n      return dueDate >= today && dueDate <= weekFromNow && !r.is_completed;\n    });\n\n    setHealthSummary({\n      totalAnimals: animalsData.length,\n      healthyAnimals: animalsData.filter(a => a.status === 'healthy').length,\n      sickAnimals: animalsData.filter(a => a.status === 'sick').length,\n      overdueVaccinations: overdueReminders.length,\n      upcomingVaccinations: upcomingReminders.length,\n      totalReminders: healthReminders.length\n    });\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'bg-red-100 text-red-800 border-red-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'low': return 'bg-green-100 text-green-800 border-green-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getReminderIcon = (type: string) => {\n    switch (type) {\n      case 'vaccination': return Shield;\n      case 'health_check': return Stethoscope;\n      case 'treatment': return Heart;\n      default: return Calendar;\n    }\n  };\n\n  if (loading && !healthSummary) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Sağlık verileri yükleniyor...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Sağlık Yönetimi</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Hayvan sağlığı, aşı takvimleri ve veteriner işlemleri\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/health/vaccines\"\n            className=\"flex items-center space-x-2 px-4 py-2 border border-green-600 text-green-600 rounded-md hover:bg-green-50 transition-colors\"\n          >\n            <Shield className=\"h-4 w-4\" />\n            <span>Aşı Yönetimi</span>\n          </Link>\n          <Link\n            href=\"/health/records\"\n            className=\"btn-primary text-white px-4 py-2 rounded-md\"\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Sağlık Kaydı Ekle\n          </Link>\n        </div>\n      </div>\n\n      {/* Çiftlik Seçimi */}\n      {farms.length > 0 && (\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-4\">\n            <label className=\"text-sm font-medium text-gray-700\">Çiftlik:</label>\n            <select\n              value={selectedFarmId}\n              onChange={(e) => setSelectedFarmId(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            >\n              {farms.map((farm) => (\n                <option key={farm.id} value={farm.id}>\n                  {farm.name}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <p className=\"text-red-800 text-sm\">{error}</p>\n        </div>\n      )}\n\n      {/* Sağlık Özeti */}\n      {healthSummary && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {/* Toplam Hayvan */}\n          <div className=\"content-overlay p-6 hover-glow\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <Users className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Toplam Hayvan</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{healthSummary.totalAnimals}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Sağlıklı Hayvanlar */}\n          <div className=\"content-overlay p-6 hover-glow\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <CheckCircle className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Sağlıklı</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{healthSummary.healthyAnimals}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Hasta Hayvanlar */}\n          <div className=\"content-overlay p-6 hover-glow\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-red-100 rounded-lg\">\n                <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Hasta</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{healthSummary.sickAnimals}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Geciken Aşılar */}\n          <div className=\"content-overlay p-6 hover-glow\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <Clock className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Geciken Aşılar</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{healthSummary.overdueVaccinations}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Yaklaşan Hatırlatmalar */}\n      <div className=\"content-overlay p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Yaklaşan Sağlık Hatırlatmaları</h2>\n          <Link\n            href=\"/reminders\"\n            className=\"text-green-600 hover:text-green-900 text-sm font-medium\"\n          >\n            Tümünü Görüntüle →\n          </Link>\n        </div>\n\n        {reminders.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Calendar className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Hatırlatma yok</h3>\n            <p className=\"text-gray-600\">Şu anda bekleyen sağlık hatırlatması bulunmuyor.</p>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {reminders.slice(0, 5).map((reminder) => {\n              const Icon = getReminderIcon(reminder.reminder_type);\n              const isOverdue = new Date(reminder.due_date) < new Date();\n              \n              return (\n                <div\n                  key={reminder.id}\n                  className={`flex items-center justify-between p-4 rounded-lg border ${\n                    isOverdue ? 'bg-red-50 border-red-200' : 'bg-white border-gray-200'\n                  }`}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`p-2 rounded-lg ${\n                      isOverdue ? 'bg-red-100' : 'bg-blue-100'\n                    }`}>\n                      <Icon className={`h-5 w-5 ${\n                        isOverdue ? 'text-red-600' : 'text-blue-600'\n                      }`} />\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">{reminder.title}</h4>\n                      <p className=\"text-sm text-gray-600\">\n                        {reminder.tag} - {new Date(reminder.due_date).toLocaleDateString('tr-TR')}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${\n                      getPriorityColor(reminder.priority)\n                    }`}>\n                      {reminder.priority === 'high' ? 'Yüksek' : \n                       reminder.priority === 'medium' ? 'Orta' : 'Düşük'}\n                    </span>\n                    {isOverdue && (\n                      <span className=\"px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 border border-red-200\">\n                        Gecikmiş\n                      </span>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Hızlı Erişim */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <Link href=\"/health/vaccines\" className=\"content-overlay p-6 hover-glow block\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-3 bg-blue-100 rounded-lg\">\n              <Shield className=\"h-8 w-8 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Aşı Yönetimi</h3>\n              <p className=\"text-gray-600 text-sm\">Aşı takvimleri ve kayıtları</p>\n            </div>\n          </div>\n        </Link>\n\n        <Link href=\"/health/records\" className=\"content-overlay p-6 hover-glow block\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-3 bg-green-100 rounded-lg\">\n              <Stethoscope className=\"h-8 w-8 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Sağlık Kayıtları</h3>\n              <p className=\"text-gray-600 text-sm\">Hastalık ve tedavi kayıtları</p>\n            </div>\n          </div>\n        </Link>\n\n        <Link href=\"/reminders\" className=\"content-overlay p-6 hover-glow block\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-3 bg-purple-100 rounded-lg\">\n              <Calendar className=\"h-8 w-8 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Hatırlatmalar</h3>\n              <p className=\"text-gray-600 text-sm\">Tüm hatırlatmaları görüntüle</p>\n            </div>\n          </div>\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAhBA;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,gBAAgB;gBAClB;YACF;QACF;yCAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,yHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,CAAC,kBAAkB,UAAU,MAAM,GAAG,GAAG;gBAC3C,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YAEX,gCAAgC;YAChC,MAAM,CAAC,aAAa,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrD,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,gBAAgB,KAAK,CAAC,IAAM,EAAE;gBACzD,yHAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC,gBAAgB,KAAK,CAAC,IAAM,EAAE;aAC5D;YAED,WAAW;YACX,aAAa;YAEb,yBAAyB;YACzB,uBAAuB,aAAa;QAEtC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,CAAC,aAAuB;QACrD,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAA,IAC3C,EAAE,aAAa,KAAK,iBAAiB,EAAE,aAAa,KAAK;QAG3D,MAAM,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,IAC9C,IAAI,KAAK,EAAE,QAAQ,IAAI,IAAI,UAAU,CAAC,EAAE,YAAY;QAGtD,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAA;YAC/C,MAAM,UAAU,IAAI,KAAK,EAAE,QAAQ;YACnC,MAAM,QAAQ,IAAI;YAClB,MAAM,cAAc,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YAClE,OAAO,WAAW,SAAS,WAAW,eAAe,CAAC,EAAE,YAAY;QACtE;QAEA,iBAAiB;YACf,cAAc,YAAY,MAAM;YAChC,gBAAgB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YACtE,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;YAChE,qBAAqB,iBAAiB,MAAM;YAC5C,sBAAsB,kBAAkB,MAAM;YAC9C,gBAAgB,gBAAgB,MAAM;QACxC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAe,OAAO,yMAAA,CAAA,SAAM;YACjC,KAAK;gBAAgB,OAAO,mNAAA,CAAA,cAAW;YACvC,KAAK;gBAAa,OAAO,uMAAA,CAAA,QAAK;YAC9B;gBAAS,OAAO,6MAAA,CAAA,WAAQ;QAC1B;IACF;IAEA,IAAI,WAAW,CAAC,eAAe;QAC7B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCAAoC;;;;;;sCACrD,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4BACjD,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oCAAqB,OAAO,KAAK,EAAE;8CACjC,KAAK,IAAI;mCADC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;YAU7B,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;YAKxC,+BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,cAAc,YAAY;;;;;;;;;;;;;;;;;;;;;;;kCAMjF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;kCAMnF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;kCAMhF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;oBAKF,UAAU,MAAM,KAAK,kBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B,6LAAC;wBAAI,WAAU;kCACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;4BAC1B,MAAM,OAAO,gBAAgB,SAAS,aAAa;4BACnD,MAAM,YAAY,IAAI,KAAK,SAAS,QAAQ,IAAI,IAAI;4BAEpD,qBACE,6LAAC;gCAEC,WAAW,CAAC,wDAAwD,EAClE,YAAY,6BAA6B,4BACzC;;kDAEF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,eAAe,EAC9B,YAAY,eAAe,eAC3B;0DACA,cAAA,6LAAC;oDAAK,WAAW,CAAC,QAAQ,EACxB,YAAY,iBAAiB,iBAC7B;;;;;;;;;;;0DAEJ,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,SAAS,KAAK;;;;;;kEACzD,6LAAC;wDAAE,WAAU;;4DACV,SAAS,GAAG;4DAAC;4DAAI,IAAI,KAAK,SAAS,QAAQ,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;kDAIvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,CAAC,kDAAkD,EAClE,iBAAiB,SAAS,QAAQ,GAClC;0DACC,SAAS,QAAQ,KAAK,SAAS,WAC/B,SAAS,QAAQ,KAAK,WAAW,SAAS;;;;;;4CAE5C,2BACC,6LAAC;gDAAK,WAAU;0DAA2F;;;;;;;;;;;;;+BA5B1G,SAAS,EAAE;;;;;wBAmCtB;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAmB,WAAU;kCACtC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAkB,WAAU;kCACrC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAa,WAAU;kCAChC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GAvUwB;KAAA", "debugId": null}}]}