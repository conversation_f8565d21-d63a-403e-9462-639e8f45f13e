'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Building2,
  Beef,
  Wheat,
  TrendingUp,
  AlertCircle,
  Plus,
  Bell,
  Shield,
  Heart,
  Calendar,
  Clock
} from 'lucide-react';
import { farmApi, reminderApi } from '@/services/api';

export default function Home() {
  const [reminders, setReminders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecentReminders();
  }, []);

  const loadRecentReminders = async () => {
    try {
      const remindersData = await reminderApi.getAllReminders();
      // Sadece tamamlanmamış ve yaklaşan hatırlatmaları al
      const upcomingReminders = remindersData
        .filter((r: any) => !r.is_completed)
        .sort((a: any, b: any) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime())
        .slice(0, 5);
      setReminders(upcomingReminders);
    } catch (err) {
      console.error('Error loading reminders:', err);
    } finally {
      setLoading(false);
    }
  };

  const getReminderIcon = (type: string) => {
    switch (type) {
      case 'vaccination': return Shield;
      case 'health_check': return Heart;
      case 'treatment': return Heart;
      default: return Bell;
    }
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center py-12">
        <Building2 className="h-16 w-16 text-green-600 mx-auto mb-4" />
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Hayvan Yetiştiriciliği Simülasyon Sistemi
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Sığır yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi.
          Çiftliğinizi yönetin, hayvanlarınızı takip edin ve finansal analizler yapın.
        </p>
        <Link
          href="/farms"
          className="inline-flex items-center space-x-2 btn-primary text-white px-8 py-4 rounded-lg text-lg font-medium"
        >
          <Plus className="h-6 w-6" />
          <span>Başlayın</span>
        </Link>
      </div>

      {/* Hatırlatmalar Widget */}
      {!loading && reminders.length > 0 && (
        <div className="content-overlay p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-orange-600" />
              <h2 className="text-lg font-semibold text-gray-900">Yaklaşan Hatırlatmalar</h2>
            </div>
            <Link
              href="/reminders"
              className="text-green-600 hover:text-green-900 text-sm font-medium"
            >
              Tümünü Görüntüle →
            </Link>
          </div>

          <div className="space-y-3">
            {reminders.map((reminder) => {
              const Icon = getReminderIcon(reminder.reminder_type);
              const overdue = isOverdue(reminder.due_date);

              return (
                <div
                  key={reminder.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    overdue ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      overdue ? 'bg-red-100' : 'bg-blue-100'
                    }`}>
                      <Icon className={`h-4 w-4 ${
                        overdue ? 'text-red-600' : 'text-blue-600'
                      }`} />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 text-sm">{reminder.title}</h4>
                      <p className="text-xs text-gray-600">
                        {reminder.tag} - {new Date(reminder.due_date).toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                  </div>
                  {overdue && (
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 border border-red-200">
                      Gecikmiş
                    </span>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Building2 className="h-12 w-12 text-blue-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Çiftlik Yönetimi
          </h3>
          <p className="text-gray-600">
            Çiftlik altyapısını, kapasiteyi ve işletme maliyetlerini yönetin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Beef className="h-12 w-12 text-red-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Hayvan Takibi
          </h3>
          <p className="text-gray-600">
            Hayvanlarınızın sağlık durumunu, performansını ve üreme kayıtlarını takip edin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Wheat className="h-12 w-12 text-yellow-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Yem Planlaması
          </h3>
          <p className="text-gray-600">
            Optimal rasyon hesaplamaları yapın ve yem maliyetlerini optimize edin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Heart className="h-12 w-12 text-red-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Sağlık Yönetimi
          </h3>
          <p className="text-gray-600">
            Aşı takvimleri, sağlık kayıtları ve veteriner işlemlerini yönetin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Finansal Simülasyon
          </h3>
          <p className="text-gray-600">
            Monte Carlo simülasyonu ile finansal projeksiyonlar ve risk analizi yapın.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Bell className="h-12 w-12 text-orange-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Hatırlatma Sistemi
          </h3>
          <p className="text-gray-600">
            Aşı, tedavi ve bakım hatırlatmalarını otomatik olarak takip edin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-slow">
            <span className="text-purple-600 text-2xl">📊</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Detaylı Raporlar
          </h3>
          <p className="text-gray-600">
            Performans raporları, karlılık analizi ve karar destek araçları.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <div className="h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-slow">
            <span className="text-indigo-600 text-2xl">🔬</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Bilimsel Hesaplamalar
          </h3>
          <p className="text-gray-600">
            NRC standartlarına uygun besin değeri hesaplamaları ve optimizasyon.
          </p>
        </div>
      </div>

      {/* CTA Section */}
      <div className="content-overlay hover-glow p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Hemen Başlayın
        </h2>
        <p className="text-gray-600 mb-6">
          Çiftliğinizi kaydedin ve hayvan yetiştiriciliğinizi optimize etmeye başlayın.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/farms"
            className="btn-primary text-white px-6 py-3 rounded-md transition-all"
          >
            Çiftlik Oluştur
          </Link>
          <Link
            href="/health"
            className="border border-red-600 text-red-600 px-6 py-3 rounded-md hover:bg-red-50 transition-colors hover-glow"
          >
            Sağlık Yönetimi
          </Link>
          <Link
            href="/simulation"
            className="border border-green-600 text-green-600 px-6 py-3 rounded-md hover:bg-green-50 transition-colors hover-glow"
          >
            Demo Simülasyon
          </Link>
        </div>
      </div>
    </div>
  );
}
