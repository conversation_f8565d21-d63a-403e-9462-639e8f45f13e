{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/feeds/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { ArrowLeft, Edit, Trash2, Wheat, DollarSign } from 'lucide-react';\nimport { feedApi } from '@/services/api';\nimport { Feed } from '@/types';\n\nexport default function FeedDetailPage() {\n  const params = useParams();\n  const feedId = params.id as string;\n  \n  const [feed, setFeed] = useState<Feed | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadFeed();\n  }, [feedId]);\n\n  const loadFeed = async () => {\n    try {\n      setLoading(true);\n      const feedData = await feedApi.getFeed(feedId);\n      setFeed(feedData);\n    } catch (err) {\n      setError('Yem bilgileri yüklenirken hata oluştu');\n      console.error('Error loading feed:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFeed = async () => {\n    if (!confirm('Bu yemi silmek istediğinizden emin misiniz?')) {\n      return;\n    }\n\n    try {\n      await feedApi.deleteFeed(feedId);\n      // Yem listesine geri dön\n      window.history.back();\n    } catch (err) {\n      alert('Yem silinirken hata oluştu');\n      console.error('Error deleting feed:', err);\n    }\n  };\n\n  const getFeedTypeDisplayName = (type: string) => {\n    const typeNames: Record<string, string> = {\n      'concentrate': 'Konsantre',\n      'roughage': 'Kaba Yem',\n      'hay': 'Kuru Ot',\n      'silage': 'Silaj',\n      'pasture': 'Mera',\n      'mineral_vitamin': 'Mineral-Vitamin'\n    };\n    return typeNames[type] || type;\n  };\n\n  const getFeedTypeColor = (type: string) => {\n    const colors: Record<string, string> = {\n      'concentrate': 'bg-blue-100 text-blue-800',\n      'roughage': 'bg-green-100 text-green-800',\n      'hay': 'bg-yellow-100 text-yellow-800',\n      'silage': 'bg-purple-100 text-purple-800',\n      'pasture': 'bg-emerald-100 text-emerald-800',\n      'mineral_vitamin': 'bg-red-100 text-red-800'\n    };\n    return colors[type] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Yem bilgileri yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !feed) {\n    return (\n      <div className=\"text-center py-12\">\n        <Wheat className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          {error || 'Yem bulunamadı'}\n        </h3>\n        <p className=\"text-gray-600 mb-6\">\n          Aradığınız yem mevcut değil veya bir hata oluştu\n        </p>\n        <Link\n          href=\"/feeds\"\n          className=\"btn-primary text-white px-6 py-3 rounded-md\"\n        >\n          Yem Listesine Dön\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Link\n            href={`/feeds?farm=${feed.farm_id}`}\n            className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">{feed.name}</h1>\n            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getFeedTypeColor(feed.feed_type)}`}>\n              {getFeedTypeDisplayName(feed.feed_type)}\n            </span>\n          </div>\n        </div>\n        \n        <div className=\"flex space-x-2\">\n          <Link\n            href={`/feeds/${feed.id}/edit`}\n            className=\"inline-flex items-center space-x-2 border border-blue-600 text-blue-600 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors\"\n          >\n            <Edit className=\"h-4 w-4\" />\n            <span>Düzenle</span>\n          </Link>\n          <button\n            onClick={handleDeleteFeed}\n            className=\"inline-flex items-center space-x-2 border border-red-600 text-red-600 px-4 py-2 rounded-md hover:bg-red-50 transition-colors\"\n          >\n            <Trash2 className=\"h-4 w-4\" />\n            <span>Sil</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Feed Details */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Temel Bilgiler */}\n        <div className=\"content-overlay p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n            <Wheat className=\"h-6 w-6 mr-2 text-yellow-600\" />\n            Temel Bilgiler\n          </h2>\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Yem Adı:</span>\n              <span className=\"font-medium\">{feed.name}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Tür:</span>\n              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getFeedTypeColor(feed.feed_type)}`}>\n                {getFeedTypeDisplayName(feed.feed_type)}\n              </span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Maliyet:</span>\n              <span className=\"font-medium flex items-center\">\n                <DollarSign className=\"h-4 w-4 mr-1\" />\n                {feed.cost_per_kg.toFixed(2)} ₺/kg\n              </span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Depolama Ömrü:</span>\n              <span className=\"font-medium\">{feed.storage_life_days} gün</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Nem İçeriği:</span>\n              <span className=\"font-medium\">{feed.moisture_content_percentage}%</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Besin Değerleri */}\n        <div className=\"content-overlay p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n            <div className=\"h-6 w-6 mr-2 bg-green-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-green-600 text-sm font-bold\">%</span>\n            </div>\n            Besin Değerleri\n          </h2>\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Kuru Madde:</span>\n              <span className=\"font-medium\">{feed.dry_matter_percentage}%</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Ham Protein:</span>\n              <span className=\"font-medium\">{feed.crude_protein_percentage}%</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Metabolik Enerji:</span>\n              <span className=\"font-medium\">{feed.metabolizable_energy_mcal_kg} Mcal/kg</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Ek Bilgiler */}\n      <div className=\"content-overlay p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Ek Bilgiler</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n            <div className=\"text-2xl font-bold text-blue-600\">{feed.dry_matter_percentage}%</div>\n            <div className=\"text-sm text-gray-600\">Kuru Madde</div>\n          </div>\n          <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n            <div className=\"text-2xl font-bold text-green-600\">{feed.crude_protein_percentage}%</div>\n            <div className=\"text-sm text-gray-600\">Ham Protein</div>\n          </div>\n          <div className=\"text-center p-4 bg-yellow-50 rounded-lg\">\n            <div className=\"text-2xl font-bold text-yellow-600\">{feed.metabolizable_energy_mcal_kg}</div>\n            <div className=\"text-sm text-gray-600\">ME (Mcal/kg)</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Geri Dön Butonu */}\n      <div className=\"flex justify-center\">\n        <Link\n          href={`/feeds?farm=${feed.farm_id}`}\n          className=\"inline-flex items-center space-x-2 bg-gray-100 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-200 transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n          <span>Yem Listesine Dön</span>\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAO;IAEX,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YACvC,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,gDAAgD;YAC3D;QACF;QAEA,IAAI;YACF,MAAM,yHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YACzB,yBAAyB;YACzB,OAAO,OAAO,CAAC,IAAI;QACrB,EAAE,OAAO,KAAK;YACZ,MAAM;YACN,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,YAAoC;YACxC,eAAe;YACf,YAAY;YACZ,OAAO;YACP,UAAU;YACV,WAAW;YACX,mBAAmB;QACrB;QACA,OAAO,SAAS,CAAC,KAAK,IAAI;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,eAAe;YACf,YAAY;YACZ,OAAO;YACP,UAAU;YACV,WAAW;YACX,mBAAmB;QACrB;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,6LAAC;oBAAG,WAAU;8BACX,SAAS;;;;;;8BAEZ,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE;gCACnC,WAAU;0CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,KAAK,IAAI;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,KAAK,SAAS,GAAG;kDAC5G,uBAAuB,KAAK,SAAS;;;;;;;;;;;;;;;;;;kCAK5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;gCAC9B,WAAU;;kDAEV,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;;;;;;kDAE1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAW,CAAC,6CAA6C,EAAE,iBAAiB,KAAK,SAAS,GAAG;0DAChG,uBAAuB,KAAK,SAAS;;;;;;;;;;;;kDAG1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDACrB,KAAK,WAAW,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAGjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAe,KAAK,iBAAiB;oDAAC;;;;;;;;;;;;;kDAExD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAe,KAAK,2BAA2B;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;oCAC/C;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAe,KAAK,qBAAqB;oDAAC;;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAe,KAAK,wBAAwB;oDAAC;;;;;;;;;;;;;kDAE/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAe,KAAK,4BAA4B;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAoC,KAAK,qBAAqB;4CAAC;;;;;;;kDAC9E,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAqC,KAAK,wBAAwB;4CAAC;;;;;;;kDAClF,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsC,KAAK,4BAA4B;;;;;;kDACtF,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE;oBACnC,WAAU;;sCAEV,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAjOwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}